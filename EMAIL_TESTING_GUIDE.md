# 📧 Email Functionality Testing Guide

## Overview
This guide provides step-by-step instructions for testing the comprehensive email notification system for the 3Shul booking platform.

## ✅ What's Been Implemented

### Edge Functions Deployed:
1. **`send-payment-email`** - Payment confirmation emails (customer + admin)
2. **`send-email`** - Generic email function for admin app
3. **`send-booking-notification`** - Comprehensive status change notifications

### Email Templates Available:
- **Payment Confirmation** (customer + admin)
- **Booking Approval** (customer + admin)
- **Booking Fulfillment** (customer + admin)
- **Booking Cancellation** (customer + admin)

## 🧪 Testing Scenarios

### 1. Payment Confirmation Email Test

**Trigger:** When a customer completes payment
**Function:** `send-payment-email`
**Expected:** 2 emails sent (customer + admin)

```bash
# Test with actual booking ID
node test-email-functions.js
```

### 2. Booking Approval Email Test

**Trigger:** Admin approves a booking in admin dashboard
**Function:** Admin app calls `send-email`
**Expected:** 2 emails sent (customer + admin)

**Steps:**
1. Open admin dashboard: `http://localhost:3000` (3shul_admin)
2. Navigate to Bookings page
3. Find a "Pending" booking
4. Click "Approve" button
5. Confirm approval in modal
6. Check console logs for email success

### 3. Booking Fulfillment Email Test

**Trigger:** Admin marks booking as fulfilled
**Function:** Admin app calls new fulfillment email functions
**Expected:** 2 emails sent (customer + admin)

**Steps:**
1. Open admin dashboard
2. Find an "Approved" booking
3. Click "Mark as Fulfilled" button
4. Confirm fulfillment in modal
5. Check console logs for email success

### 4. Booking Cancellation Email Test

**Trigger:** Admin cancels/rejects a booking
**Function:** Admin app calls new cancellation email functions
**Expected:** 2 emails sent (customer + admin)

**Steps:**
1. Open admin dashboard
2. Find a "Pending" or "Approved" booking
3. Click "Reject" button (X icon)
4. Confirm cancellation in modal
5. Check console logs for email success

## 🔧 Manual Testing with Real Data

### Step 1: Get a Real Booking ID

```sql
-- Run this query in Supabase SQL Editor
SELECT id, business_name, email, status, start_date, end_date 
FROM bookings 
WHERE email IS NOT NULL 
LIMIT 5;
```

### Step 2: Test Payment Email Function

```javascript
// Replace with actual booking ID
const testBookingId = 'your-actual-booking-id-here';

fetch('https://xirnspqtaoyzsqlbbhcl.supabase.co/functions/v1/send-payment-email', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_ANON_KEY'
  },
  body: JSON.stringify({
    bookingId: testBookingId
  })
})
.then(res => res.json())
.then(data => console.log('Payment email result:', data));
```

### Step 3: Test Status Change Notifications

```javascript
// Test approval notification
fetch('https://xirnspqtaoyzsqlbbhcl.supabase.co/functions/v1/send-booking-notification', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_ANON_KEY'
  },
  body: JSON.stringify({
    bookingId: testBookingId,
    notificationType: 'approved' // or 'fulfilled', 'cancelled'
  })
})
.then(res => res.json())
.then(data => console.log('Notification result:', data));
```

## 📋 Verification Checklist

### Email Content Verification:
- [ ] Customer emails have correct business name
- [ ] Zone information displays properly (single or multiple zones)
- [ ] Dates are formatted correctly
- [ ] Contact information (email, phone) is included
- [ ] Status-specific messaging is appropriate
- [ ] Branding and styling is consistent

### Technical Verification:
- [ ] Both customer and admin receive emails
- [ ] Email headers include booking ID
- [ ] Error handling works for missing data
- [ ] Console logs show successful email sending
- [ ] Database relationships (zones, booking_zones) work correctly

### Admin Dashboard Verification:
- [ ] Email configuration status shows correctly
- [ ] Confirmation modals show appropriate messaging
- [ ] Processing states work during email sending
- [ ] Success/error feedback is provided

## 🚨 Troubleshooting

### Common Issues:

1. **"Email not configured" error**
   - Check admin dashboard email configuration
   - Verify SMTP credentials are set

2. **"Booking not found" error**
   - Ensure booking ID exists in database
   - Check booking has required fields (email, business_name)

3. **"Zone not found" error**
   - Verify booking has zone relationships
   - Check zones or booking_zones tables

4. **Email not received**
   - Check spam/junk folders
   - Verify email addresses are correct
   - Check Supabase function logs

### Debug Commands:

```bash
# Check Supabase function logs
npx supabase functions logs send-payment-email
npx supabase functions logs send-booking-notification
npx supabase functions logs send-email
```

## 📊 Success Criteria

✅ **All tests pass when:**
- Payment confirmation emails sent to customer + admin
- Approval emails sent to customer + admin  
- Fulfillment emails sent to customer + admin
- Cancellation emails sent to customer + admin
- Email templates display correct booking information
- Zone information handles both single and multiple zones
- Admin dashboard shows email status correctly
- Error handling works for edge cases

## 🎯 Next Steps

After successful testing:
1. Monitor email delivery rates
2. Collect user feedback on email content
3. Consider adding email preferences/unsubscribe
4. Implement email analytics/tracking
5. Add email templates for other status changes if needed
