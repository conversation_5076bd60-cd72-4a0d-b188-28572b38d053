import { useState, useEffect } from 'react';
import { Screen, City, CartItem, DateRange, BusinessInfo, Zone } from '../types';
import { Search, Monitor, ChevronLeft, ChevronRight, Check, AlertCircle } from 'lucide-react';
import { MOCK_SCREENS, MOCK_ZONES } from '../mockData';
import { supabase } from '../lib/supabase';

interface ScreenListProps {
  selectedCities: City[];
  dateRange: DateRange;
  cartItems: CartItem[];
  onScreensSelected: (screens: Screen[]) => void;
  onNext: () => void;
  businessInfo?: BusinessInfo;
}

export function ScreenList({
  selectedCities,
  dateRange,
  cartItems,
  onScreensSelected,
  onNext,
  businessInfo
}: ScreenListProps) {
  const [screens, setScreens] = useState<Screen[]>([]);
  const [filteredScreens, setFilteredScreens] = useState<Screen[]>([]);
  const [selectedScreens, setSelectedScreens] = useState<Screen[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectAll, setSelectAll] = useState(false);

  const screensPerPage = 20;
  const minScreens = 25;

  useEffect(() => {
    const fetchScreensForSelectedCities = async () => {
      if (selectedCities.length === 0) {
        setScreens([]);
        setFilteredScreens([]);
        return;
      }

      try {
        // Get the selected city IDs
        const selectedCityIds = selectedCities.map(city => city.id);

        // First, fetch zones that belong to the selected cities
        const { data: zonesData, error: zonesError } = await supabase
          .from('zones')
          .select('id')
          .in('city_id', selectedCityIds)
          .eq('isavailable', true);

        if (zonesError) {
          console.error('Error fetching zones:', zonesError);
          // Fallback to mock data filtering
          const cityZoneIds = selectedCities.map(city => {
            if (city.name === 'Mumbai') return ['1', '2'];
            if (city.name === 'Delhi') return ['3'];
            return [];
          }).flat();

          const availableScreens = MOCK_SCREENS.filter(screen =>
            screen.isavailable && cityZoneIds.includes(screen.zone_id)
          );

          setScreens(availableScreens);
          setFilteredScreens(availableScreens);
          return;
        }

        const zoneIds = zonesData?.map(zone => zone.id) || [];

        // Now fetch screens that belong to these zones
        const { data: screensData, error: screensError } = await supabase
          .from('screens')
          .select('*')
          .in('zone_id', zoneIds)
          .eq('isavailable', true);

        if (screensError) {
          console.error('Error fetching screens:', screensError);
          // Fallback to mock data filtering
          const availableScreens = MOCK_SCREENS.filter(screen =>
            screen.isavailable && zoneIds.includes(screen.zone_id)
          );

          setScreens(availableScreens);
          setFilteredScreens(availableScreens);
          return;
        }

        // Use database data if available, otherwise fallback to mock data filtered by zones
        const availableScreens = screensData && screensData.length > 0
          ? screensData
          : MOCK_SCREENS.filter(screen =>
              screen.isavailable && zoneIds.includes(screen.zone_id)
            );

        setScreens(availableScreens);
        setFilteredScreens(availableScreens);

      } catch (err) {
        console.error('Database connection error:', err);
        // Fallback to mock data filtering
        const cityZoneIds = selectedCities.map(city => {
          if (city.name === 'Mumbai') return ['1', '2'];
          if (city.name === 'Delhi') return ['3'];
          return [];
        }).flat();

        const availableScreens = MOCK_SCREENS.filter(screen =>
          screen.isavailable && cityZoneIds.includes(screen.zone_id)
        );

        setScreens(availableScreens);
        setFilteredScreens(availableScreens);
      }
    };

    fetchScreensForSelectedCities();
  }, [selectedCities]);

  useEffect(() => {
    // Filter screens based on search term
    const filtered = screens.filter(screen =>
      screen.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      screen.zone_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      screen.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
      screen.location.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredScreens(filtered);
    setCurrentPage(1); // Reset to first page when filtering
  }, [searchTerm, screens]);

  const totalPages = Math.ceil(filteredScreens.length / screensPerPage);
  const startIndex = (currentPage - 1) * screensPerPage;
  const endIndex = startIndex + screensPerPage;
  const currentScreens = filteredScreens.slice(startIndex, endIndex);

  const handleScreenToggle = (screen: Screen) => {
    const isSelected = selectedScreens.some(s => s.id === screen.id);

    if (isSelected) {
      const newSelection = selectedScreens.filter(s => s.id !== screen.id);
      setSelectedScreens(newSelection);
      onScreensSelected(newSelection);
    } else {
      const newSelection = [...selectedScreens, screen];
      setSelectedScreens(newSelection);
      onScreensSelected(newSelection);
    }
  };

  const handleSelectAllPage = () => {
    if (selectAll) {
      // Deselect all screens on current page
      const currentScreenIds = currentScreens.map(s => s.id);
      const newSelection = selectedScreens.filter(s => !currentScreenIds.includes(s.id));
      setSelectedScreens(newSelection);
      onScreensSelected(newSelection);
      setSelectAll(false);
    } else {
      // Select all screens on current page
      const newScreens = currentScreens.filter(s => !selectedScreens.some(selected => selected.id === s.id));
      const newSelection = [...selectedScreens, ...newScreens];
      setSelectedScreens(newSelection);
      onScreensSelected(newSelection);
      setSelectAll(true);
    }
  };

  const isScreenSelected = (screenId: string) => {
    return selectedScreens.some(s => s.id === screenId);
  };

  const canProceed = selectedScreens.length >= minScreens;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900">Select Screens</h2>
        <p className="mt-2 text-gray-600">
          Choose at least {minScreens} screens from the selected cities
        </p>
      </div>

      {/* Package info banner */}
      <div className="p-4 rounded-lg bg-green-50 border border-green-200">
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 flex-shrink-0 text-green-500" />
          <div>
            <h3 className="text-sm font-medium mb-1">Per-Screen Package</h3>
            <p className="text-sm text-gray-600">
              ₹500 per screen with minimum {minScreens} screens required.
              Currently showing screens from: {selectedCities[0]?.name || 'Selected City'}
            </p>
          </div>
        </div>
      </div>

      {/* Search and Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search screens by name, zone, address, or location..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
          />
        </div>

        <button
          onClick={handleSelectAllPage}
          className="px-4 py-2 text-sm font-medium text-purple-600 bg-purple-50 border border-purple-200 rounded-md hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          {selectAll ? 'Deselect All on Page' : 'Select All on Page'}
        </button>
      </div>

      {/* Selection Summary */}
      <div className="bg-white rounded-lg border p-4">
        <div className="flex items-center justify-between">
          <div>
            <span className="text-sm font-medium text-gray-700">
              {selectedScreens.length} screens selected
            </span>
            {selectedScreens.length < minScreens && (
              <span className="ml-2 text-sm text-red-600">
                (Need {minScreens - selectedScreens.length} more)
              </span>
            )}
          </div>
          <div className="text-sm text-gray-600">
            Total cost: ₹{(selectedScreens.length * 500).toLocaleString('en-IN')}
          </div>
        </div>
      </div>

      {/* Screens Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {currentScreens.map((screen) => {
          const isSelected = isScreenSelected(screen.id);

          return (
            <div
              key={screen.id}
              onClick={() => handleScreenToggle(screen)}
              className={`p-4 rounded-lg border cursor-pointer transition-all hover:shadow-sm ${
                isSelected
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-200 hover:border-purple-300'
              }`}
            >
              <div className="flex justify-between items-start mb-2">
                <div className="flex items-center">
                  <Monitor className="h-4 w-4 text-gray-500 mr-1.5" />
                  <h4 className="font-medium text-gray-900 text-sm">{screen.name}</h4>
                </div>
                <div className={`h-5 w-5 rounded-full flex items-center justify-center ${
                  isSelected ? 'bg-purple-600' : 'border border-gray-300'
                }`}>
                  {isSelected && <Check className="h-3 w-3 text-white" />}
                </div>
              </div>

              <div className="space-y-1 text-xs text-gray-600">
                <p><span className="font-medium">Zone:</span> {screen.zone_name}</p>
                <p><span className="font-medium">Screen #:</span> {screen.screen_number}</p>
                <p><span className="font-medium">Location:</span> {screen.location}</p>
                <p><span className="font-medium">Address:</span> {screen.address}</p>
                <p><span className="font-medium">Size:</span> {screen.size}</p>
              </div>
            </div>
          );
        })}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {startIndex + 1} to {Math.min(endIndex, filteredScreens.length)} of {filteredScreens.length} screens
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="p-2 rounded-md border border-gray-300 text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>

            <span className="px-3 py-1 text-sm font-medium">
              Page {currentPage} of {totalPages}
            </span>

            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="p-2 rounded-md border border-gray-300 text-gray-500 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Continue Button */}
      {canProceed && (
        <div className="flex justify-end">
          <button
            onClick={onNext}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            Continue to Cart
          </button>
        </div>
      )}
    </div>
  );
}
