import { supabase } from './supabase';
import { Coupon, CouponUsage, CouponTier, CouponValidationR<PERSON>ult, AppliedCoupon } from '../types';

export async function validateCoupon(
  couponCode: string, 
  userId: string, 
  cartTotal: number
): Promise<CouponValidationResult> {
  try {
    // Fetch coupon by code
    const { data: coupon, error: couponError } = await supabase
      .from('coupons')
      .select('*')
      .eq('code', couponCode.toUpperCase())
      .eq('is_active', true)
      .single();

    if (couponError || !coupon) {
      return { valid: false, error: 'Invalid coupon code' };
    }

    // Check if coupon is within valid date range
    const now = new Date();
    const startDate = new Date(coupon.start_date);
    const expiryDate = new Date(coupon.expiry_date);

    if (now < startDate) {
      return { valid: false, error: 'Coupon is not yet active' };
    }

    if (now > expiryDate) {
      return { valid: false, error: 'Coupon has expired' };
    }

    // Check minimum cart value
    if (cartTotal < coupon.min_cart_value) {
      return { 
        valid: false, 
        error: `Minimum cart value of ₹${coupon.min_cart_value} required` 
      };
    }

    // Check global usage limit
    if (coupon.global_usage_limit) {
      const { count: globalUsageCount } = await supabase
        .from('coupon_usages')
        .select('*', { count: 'exact', head: true })
        .eq('coupon_id', coupon.id);

      if (globalUsageCount && globalUsageCount >= coupon.global_usage_limit) {
        return { valid: false, error: 'Coupon usage limit exceeded' };
      }
    }

    // Check per-user usage limit
    let userUsage: CouponUsage | null = null;
    if (coupon.per_user_usage_limit) {
      const { data: usage } = await supabase
        .from('coupon_usages')
        .select('*')
        .eq('coupon_id', coupon.id)
        .eq('user_id', userId)
        .single();

      userUsage = usage;

      if (userUsage && userUsage.usage_count >= coupon.per_user_usage_limit) {
        return { valid: false, error: 'You have reached the usage limit for this coupon' };
      }
    }

    // Calculate discount amount
    const usageNumber = userUsage ? userUsage.usage_count + 1 : 1;
    const discountAmount = await calculateDiscountAmount(coupon, cartTotal, usageNumber);

    return {
      valid: true,
      discount_amount: discountAmount,
      usage_number: usageNumber
    };

  } catch (error) {
    console.error('Error validating coupon:', error);
    return { valid: false, error: 'Failed to validate coupon' };
  }
}

export async function calculateDiscountAmount(
  coupon: Coupon, 
  cartTotal: number, 
  usageNumber: number
): Promise<number> {
  try {
    // Check if there are tier-specific discounts
    const { data: tiers } = await supabase
      .from('coupon_tiers')
      .select('*')
      .eq('coupon_id', coupon.id)
      .eq('usage_number', usageNumber);

    let discountAmount = 0;

    if (tiers && tiers.length > 0) {
      // Use tier-specific discount
      const tier = tiers[0];
      if (tier.amount) {
        discountAmount = tier.amount;
      } else if (tier.percentage) {
        discountAmount = (cartTotal * tier.percentage) / 100;
        if (coupon.max_discount && discountAmount > coupon.max_discount) {
          discountAmount = coupon.max_discount;
        }
      }
    } else {
      // Use default coupon discount
      if (coupon.type === 'flat') {
        discountAmount = coupon.amount;
      } else if (coupon.type === 'percentage') {
        discountAmount = (cartTotal * coupon.amount) / 100;
        if (coupon.max_discount && discountAmount > coupon.max_discount) {
          discountAmount = coupon.max_discount;
        }
      }
    }

    // Ensure discount doesn't exceed cart total
    return Math.min(discountAmount, cartTotal);

  } catch (error) {
    console.error('Error calculating discount amount:', error);
    return 0;
  }
}

export async function applyCoupon(
  couponCode: string, 
  userId: string, 
  cartTotal: number
): Promise<AppliedCoupon | null> {
  try {
    const validation = await validateCoupon(couponCode, userId, cartTotal);
    
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    // Fetch the full coupon data
    const { data: coupon } = await supabase
      .from('coupons')
      .select('*')
      .eq('code', couponCode.toUpperCase())
      .single();

    if (!coupon) {
      throw new Error('Coupon not found');
    }

    return {
      coupon,
      discount_amount: validation.discount_amount!,
      usage_number: validation.usage_number
    };

  } catch (error) {
    console.error('Error applying coupon:', error);
    return null;
  }
}

export async function recordCouponUsage(
  couponId: string, 
  userId: string, 
  bookingId: string
): Promise<void> {
  try {
    // Check if user already has usage record for this coupon
    const { data: existingUsage } = await supabase
      .from('coupon_usages')
      .select('*')
      .eq('coupon_id', couponId)
      .eq('user_id', userId)
      .single();

    if (existingUsage) {
      // Update existing usage record
      await supabase
        .from('coupon_usages')
        .update({
          usage_count: existingUsage.usage_count + 1,
          last_used_at: new Date().toISOString()
        })
        .eq('id', existingUsage.id);
    } else {
      // Create new usage record
      await supabase
        .from('coupon_usages')
        .insert([{
          coupon_id: couponId,
          user_id: userId,
          usage_count: 1,
          last_used_at: new Date().toISOString()
        }]);
    }

  } catch (error) {
    console.error('Error recording coupon usage:', error);
    throw error;
  }
}

export function generateCouponCode(length: number = 8): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
}

export function formatDiscountDisplay(coupon: Coupon): string {
  if (coupon.type === 'flat') {
    return `₹${coupon.amount} off`;
  } else {
    const maxDiscountText = coupon.max_discount ? ` (max ₹${coupon.max_discount})` : '';
    return `${coupon.amount}% off${maxDiscountText}`;
  }
}
