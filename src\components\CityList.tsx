import { useState, useEffect } from 'react';
import { City, Zone, Screen, CartItem, DateRange, BusinessInfo, PackageType } from '../types';
import { Search, MapPin, ArrowRight } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { MOCK_CITIES } from '../mockData';

interface CityListProps {
  dateRange: DateRange;
  cartItems: CartItem[];
  onCitySelect: (city: City) => void;
  onNext: () => void;
  businessInfo?: BusinessInfo;
}

export function CityList({ dateRange, cartItems, onCitySelect, onNext, businessInfo }: CityListProps) {
  const [cities, setCities] = useState<City[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCities, setSelectedCities] = useState<City[]>([]);

  useEffect(() => {
    const fetchCities = async () => {
      try {
        const { data, error } = await supabase
          .from('cities')
          .select('*')
          .eq('isavailable', true)
          .order('name');

        if (error) {
          console.error('Error fetching cities:', error);
          // Fallback to mock data if database fails
          setCities(MOCK_CITIES);
        } else {
          setCities(data || []);
        }
      } catch (err) {
        console.error('Database connection error:', err);
        // Fallback to mock data
        setCities(MOCK_CITIES);
      }
    };

    fetchCities();
  }, []);

  // Get the selected package type
  const packageType = businessInfo?.packageType || '';
  const isFixedPricePackage = packageType === 'fixed-price';
  const isPerScreenPackage = packageType === 'per-screen';

  const filteredCities = cities.filter(city =>
    city.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    city.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCitySelect = (city: City) => {
    // Both packages allow only one city selection
    if (selectedCities.length > 0 && selectedCities[0].id !== city.id) {
      if (confirm('You can only select one city. Do you want to change your selection?')) {
        setSelectedCities([city]);
        onCitySelect(city);
      }
    } else if (selectedCities.length === 0) {
      setSelectedCities([city]);
      onCitySelect(city);
    }
  };

  const isCitySelected = (cityId: string) => {
    return selectedCities.some(c => c.id === cityId);
  };

  const canProceed = selectedCities.length > 0;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900">Select Cities</h2>
        <p className="mt-2 text-gray-600">
          Choose one city for your {isFixedPricePackage ? 'fixed-price' : 'per-screen'} package
        </p>
      </div>

      {/* Package info banner */}
      <div className={`p-4 rounded-lg ${isFixedPricePackage ? 'bg-blue-50 border border-blue-200' : 'bg-green-50 border border-green-200'}`}>
        <div className="flex items-start">
          <MapPin className={`h-5 w-5 mr-2 flex-shrink-0 ${isFixedPricePackage ? 'text-blue-500' : 'text-green-500'}`} />
          <div>
            <h3 className="text-sm font-medium mb-1">
              {isFixedPricePackage ? 'Fixed-Price Package' : 'Per-Screen Package'}
            </h3>
            <p className="text-sm text-gray-600">
              {isFixedPricePackage
                ? 'Select one city. You can then choose one or more zones within that city. Each zone costs ₹12,000 and includes all screens in that zone (~25 screens).'
                : 'Select one city. You can then choose individual screens from that city (minimum 25 screens required).'}
            </p>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          placeholder="Search cities..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
        />
      </div>

      {/* Cities Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredCities.map((city) => {
          const isSelected = isCitySelected(city.id);

          return (
            <div
              key={city.id}
              onClick={() => handleCitySelect(city)}
              className={`relative flex flex-col overflow-hidden rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                isSelected
                  ? 'border-purple-500 bg-purple-50 shadow-md'
                  : 'border-gray-200 bg-white hover:border-purple-300'
              }`}
            >
              <div className="aspect-h-3 aspect-w-4">
                <img
                  src={city.image_url}
                  alt={city.name}
                  className="h-48 w-full object-cover"
                />
              </div>
              <div className="flex flex-1 flex-col p-4">
                <h3 className="text-lg font-semibold text-gray-900">{city.name}</h3>
                <p className="mt-2 text-sm text-gray-600 flex-1">{city.description}</p>

                {isSelected && (
                  <div className="mt-3 flex items-center text-sm font-medium text-purple-700">
                    <div className="h-2 w-2 bg-purple-600 rounded-full mr-2"></div>
                    Selected
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Selected Cities Summary */}
      {selectedCities.length > 0 && (
        <div className="bg-white rounded-lg border p-4">
          <h4 className="font-medium text-gray-900 mb-2">Selected Cities:</h4>
          <div className="flex flex-wrap gap-2">
            {selectedCities.map((city) => (
              <span
                key={city.id}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800"
              >
                {city.name}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Continue Button */}
      {canProceed && (
        <div className="flex justify-end">
          <button
            onClick={onNext}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            Continue to {isFixedPricePackage ? 'Zones' : 'Screens'}
            <ArrowRight className="ml-2 h-5 w-5" />
          </button>
        </div>
      )}
    </div>
  );
}
