import { useState, useEffect } from 'react';
import { Plus, Upload, LogOut, CreditCard } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { User, Booking } from '../../types';
import { useAuth } from '../../contexts/AuthContext';
import { VideoPreview } from '../VideoPreview';
import { v4 as uuidv4 } from 'uuid';
import { supabase } from '../../lib/supabase';
import { format } from 'date-fns';
import { formatPrice, calculateTotalPrice } from '../../utils';
import { getUserBookings, updateBookingVideo } from '../../lib/bookings';
import { RAZORPAY_KEY } from '../../lib/config';

interface DashboardProps {
  user: User;
  bookings: Booking[];
  onLogout: () => void;
  onVideoUpload: (bookingId: string, file: File) => void;
  onBookingsUpdate: (bookings: Booking[]) => void;
}

export function Dashboard({ user, bookings, onLogout, onVideoUpload, onBookingsUpdate }: DashboardProps) {
  const navigate = useNavigate();
  const { logout } = useAuth();
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [showVideoPreview, setShowVideoPreview] = useState<string | null>(null);
  const [paymentFilter, setPaymentFilter] = useState<'all' | 'pending' | 'initiated' | 'success' | 'failed'>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [uploadingVideo, setUploadingVideo] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [processingPayment, setProcessingPayment] = useState<string | null>(null);

  useEffect(() => {
    if (user?.id) {
      fetchBookings();
    }
  }, [user?.id]);

  const fetchBookings = async () => {
    try {
      setIsLoading(true);
      const updatedBookings = await getUserBookings(user.id);
      console.log('Fetched bookings with payment status:', updatedBookings.map(b => ({
        id: b.id,
        business_name: b.business_name,
        payment_status: b.payment_status
      })));
      onBookingsUpdate(updatedBookings);
    } catch (error) {
      console.error('Error fetching bookings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleNewBooking = () => {
    navigate('/book');
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const loadRazorpayScript = () => {
    return new Promise((resolve) => {
      const script = document.createElement('script');
      script.src = 'https://checkout.razorpay.com/v1/checkout.js';
      script.onload = () => {
        resolve(true);
      };
      script.onerror = () => {
        resolve(false);
      };
      document.body.appendChild(script);
    });
  };

  const handlePayment = async (booking: Booking) => {
    if (!booking.zones || booking.zones.length === 0) {
      alert('No zones found for this booking');
      return;
    }

    try {
      setProcessingPayment(booking.id);

      // Load Razorpay script
      const isScriptLoaded = await loadRazorpayScript();
      if (!isScriptLoaded) {
        throw new Error('Razorpay SDK failed to load');
      }

      // Calculate total amount
      const totalAmount = booking.zones.reduce((sum, zone) => sum + zone.price_year, 0);
      const amountInPaise = Math.round(totalAmount * 100);

      const options = {
        key: RAZORPAY_KEY,
        amount: amountInPaise,
        currency: 'INR',
        name: '3Shul Customer',
        description: `Payment for ${booking.business_name || 'Booking'}`,
        image: '/logo.png',
        handler: async (response: any) => {
          try {
            // Update booking with success status
            const { error: updateError } = await supabase
              .from('bookings')
              .update({
                payment_id: response.razorpay_payment_id,
                payment_status: 'success',
                status: 'Pending',
                updated_at: new Date().toISOString()
              })
              .eq('id', booking.id);

            if (updateError) throw updateError;

            alert('Payment successful! Your booking is now pending approval.');
            fetchBookings(); // Refresh bookings
            setSelectedBooking(null); // Close modal
          } catch (error) {
            console.error('Error processing successful payment:', error);
            alert('Payment was successful, but we encountered an issue updating our records. Our team will resolve this shortly.');
          }
        },
        prefill: {
          name: booking.business_name || '',
          email: booking.email || '',
          contact: booking.phone_number || '',
        },
        notes: {
          booking_id: booking.id,
          business_name: booking.business_name || '',
        },
        theme: {
          color: '#23044b'
        },
        modal: {
          ondismiss: function() {
            console.log('Payment cancelled by user');
          }
        }
      };

      const paymentObject = new window.Razorpay(options);
      paymentObject.open();

    } catch (error) {
      console.error('Error initiating payment:', error);
      alert('Failed to initiate payment. Please try again or contact support.');
    } finally {
      setProcessingPayment(null);
    }
  };

  // Filter bookings based on payment status
  const filteredBookings = bookings.filter(booking => {
    if (paymentFilter === 'all') return true;
    // Handle null/undefined payment_status as 'pending'
    const paymentStatus = booking.payment_status || 'pending';
    return paymentStatus === paymentFilter;
  });

  const validateVideoFile = (file: File): { valid: boolean; error?: string } => {
    // Check file size (50MB limit)
    const maxSize = 50 * 1024 * 1024; // 50MB in bytes
    if (file.size > maxSize) {
      return { valid: false, error: 'File size must be less than 50MB' };
    }

    // Check file type
    const allowedTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/wmv', 'video/webm'];
    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'Please select a valid video file (MP4, MOV, AVI, WMV, WebM)' };
    }

    return { valid: true };
  };

  const handleVideoUpload = async (bookingId: string, file: File) => {
    try {
      setUploadingVideo(bookingId);
      setUploadProgress(0);

      // Validate the file
      const validation = validateVideoFile(file);
      if (!validation.valid) {
        alert(validation.error);
        return;
      }

      // Sanitize the filename before upload
      const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const videoFileName = `${uuidv4()}-${sanitizedFileName}`;

      console.log('Starting video upload for booking:', bookingId);

      // Upload the file
      const { data, error: uploadError } = await supabase.storage
        .from('videos')
        .upload(videoFileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        console.error('Upload error:', uploadError);
        throw uploadError;
      }

      console.log('File uploaded successfully:', data);

      // Get the public URL
      const { data: { publicUrl }, error: urlError } = supabase.storage
        .from('videos')
        .getPublicUrl(videoFileName);

      if (urlError) {
        console.error('URL error:', urlError);
        throw urlError;
      }

      console.log('Public URL generated:', publicUrl);

      // Update the booking with the video URL
      await updateBookingVideo(bookingId, publicUrl);

      // Refresh bookings
      await fetchBookings();

      console.log('Video uploaded and booking updated successfully');
      alert('Video uploaded successfully!');
    } catch (error) {
      console.error('Video upload failed:', error);
      alert(`Video upload failed: ${error.message || 'Unknown error'}`);
    } finally {
      setUploadingVideo(null);
      setUploadProgress(0);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleNewBooking}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
              >
                <Plus className="h-5 w-5 mr-2" />
                New Booking
              </button>
              <button
                onClick={handleLogout}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <LogOut className="h-5 w-5 mr-2" />
                Logout
              </button>
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Add filter controls */}
        <div className="mb-6">
          <div className="flex items-center space-x-4">
            <label htmlFor="payment-filter" className="text-sm font-medium text-gray-700">
              Filter by Payment Status:
            </label>
            <select
              id="payment-filter"
              value={paymentFilter}
              onChange={(e) => setPaymentFilter(e.target.value as 'all' | 'pending' | 'initiated' | 'success' | 'failed')}
              className="mt-1 block w-48 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm rounded-md"
            >
              <option value="all">All Bookings</option>
              <option value="pending">Payment Pending</option>
              <option value="initiated">Payment Initiated</option>
              <option value="success">Payment Success</option>
              <option value="failed">Payment Failed</option>
            </select>
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-12">
            <div className="spinner">Loading...</div>
          </div>
        ) : filteredBookings.length === 0 ? (
          <div className="text-center py-12">
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              {paymentFilter === 'all' 
                ? 'No bookings found' 
                : `No bookings with payment status: ${paymentFilter}`}
            </h3>
            <div className="mt-6">
              <button
                onClick={() => navigate('/book')}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700"
              >
                <Plus className="h-5 w-5 mr-2" />
                New Booking
              </button>
            </div>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredBookings.map((booking) => (
              <div
                key={booking.id}
                className="bg-white overflow-hidden shadow rounded-lg divide-y divide-gray-200 cursor-pointer hover:shadow-md transition-shadow"
                onClick={() => setSelectedBooking(booking)}
              >
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {booking.business_name || 'Unnamed Booking'}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {format(new Date(booking.start_date), 'MMM yyyy')} -{' '}
                    {format(new Date(booking.end_date), 'MMM yyyy')}
                  </p>
                  <div className="mt-2">
                    <p className="text-sm text-gray-600">
                      {booking.zones?.length || 0} zone{booking.zones?.length !== 1 ? 's' : ''} booked
                    </p>
                  </div>
                  <div className="mt-2 flex flex-col space-y-2">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        booking.status === 'Approved'
                          ? 'bg-green-100 text-green-800'
                          : booking.status === 'Failed'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}
                    >
                      Status: {booking.status}
                    </span>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        booking.payment_status === 'success'
                          ? 'bg-green-100 text-green-800'
                          : booking.payment_status === 'failed'
                          ? 'bg-red-100 text-red-800'
                          : booking.payment_status === 'initiated' || booking.payment_status === 'authorized'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}
                    >
                      Payment: {booking.payment_status || 'pending'}
                    </span>
                  </div>
                </div>
                <div className="px-4 py-4 sm:px-6">
                  {booking.video_url ? (
                    <div className="space-y-4">
                      <div className="max-h-64 overflow-hidden rounded-lg bg-gray-100">
                        <VideoPreview
                          videoUrl={booking.video_url}
                          className="w-full h-full object-contain"
                        />
                      </div>
                      <label className={`inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 cursor-pointer ${uploadingVideo === booking.id ? 'opacity-50 cursor-not-allowed' : ''}`}>
                        <Upload className="h-5 w-5 mr-2" />
                        {uploadingVideo === booking.id ? 'Uploading...' : 'Update Video'}
                        <input
                          type="file"
                          className="hidden"
                          accept="video/*"
                          disabled={uploadingVideo === booking.id}
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file && uploadingVideo !== booking.id) {
                              handleVideoUpload(booking.id, file);
                            }
                          }}
                        />
                      </label>
                    </div>
                  ) : (
                    <label className={`inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 cursor-pointer ${uploadingVideo === booking.id ? 'opacity-50 cursor-not-allowed' : ''}`}>
                      <Upload className="h-5 w-5 mr-2" />
                      {uploadingVideo === booking.id ? 'Uploading...' : 'Upload Video'}
                      <input
                        type="file"
                        className="hidden"
                        accept="video/*"
                        disabled={uploadingVideo === booking.id}
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file && uploadingVideo !== booking.id) {
                            handleVideoUpload(booking.id, file);
                          }
                        }}
                      />
                    </label>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {selectedBooking && (
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full p-6 max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-start">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">
                    {selectedBooking.business_name || 'Unnamed Booking'}
                  </h2>
                  <p className="mt-1 text-sm text-gray-500">
                    Booking ID: {selectedBooking.id}
                  </p>
                </div>
                <button
                  onClick={() => setSelectedBooking(null)}
                  className="text-gray-400 hover:text-gray-500"
                >
                  <span className="sr-only">Close</span>
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <div className="mt-6 space-y-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Booked Zones</h3>
                  <div className="mt-2 space-y-4">
                    {selectedBooking.zones && selectedBooking.zones.length > 0 ? (
                      selectedBooking.zones.map((zone) => (
                        <div key={zone.id} className="bg-gray-50 rounded-lg p-4">
                          <h4 className="font-medium text-gray-900">{zone.name}</h4>
                          <p className="mt-1 text-sm text-gray-500">
                            {zone.city} - {zone.sub_zone}
                          </p>
                          <p className="mt-1 text-sm font-medium text-gray-900">
                            {formatPrice(zone.price_year)} / year
                          </p>
                        </div>
                      ))
                    ) : (
                      <p className="text-sm text-gray-500">No zones found</p>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Booking Period</h3>
                  <p className="mt-1 text-sm text-gray-900">
                    {format(new Date(selectedBooking.start_date), 'MMMM d, yyyy')} -{' '}
                    {format(new Date(selectedBooking.end_date), 'MMMM d, yyyy')}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Business Details</h3>
                  <div className="mt-2 space-y-2">
                    <p className="text-sm text-gray-900">
                      <span className="font-medium">Phone:</span> {selectedBooking.phone_number}
                    </p>
                    <p className="text-sm text-gray-900">
                      <span className="font-medium">Listing Type:</span> {selectedBooking.listing_type}
                    </p>
                    {selectedBooking.description && (
                      <p className="text-sm text-gray-900">
                        <span className="font-medium">Description:</span> {selectedBooking.description}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Status</h3>
                  <span
                    className={`mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      selectedBooking.status === 'Approved'
                        ? 'bg-green-100 text-green-800'
                        : selectedBooking.status === 'Rejected'
                        ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}
                  >
                    {selectedBooking.status}
                  </span>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Payment Status</h3>
                  <div className="mt-1 flex items-center space-x-3">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        selectedBooking.payment_status === 'success'
                          ? 'bg-green-100 text-green-800'
                          : selectedBooking.payment_status === 'failed'
                          ? 'bg-red-100 text-red-800'
                          : selectedBooking.payment_status === 'initiated' || selectedBooking.payment_status === 'authorized'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}
                    >
                      {selectedBooking.payment_status || 'pending'}
                    </span>
                    {(selectedBooking.payment_status === 'pending' || !selectedBooking.payment_status) && (
                      <button
                        onClick={() => handlePayment(selectedBooking)}
                        disabled={processingPayment === selectedBooking.id}
                        className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white ${
                          processingPayment === selectedBooking.id
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500'
                        }`}
                      >
                        {processingPayment === selectedBooking.id ? (
                          <>
                            <div className="animate-spin rounded-full h-3 w-3 border border-white border-t-transparent mr-1"></div>
                            Processing...
                          </>
                        ) : (
                          <>
                            <CreditCard className="h-3 w-3 mr-1" />
                            Pay Now
                          </>
                        )}
                      </button>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Video</h3>
                  <div className="mt-2">
                    <label className={`inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 cursor-pointer ${uploadingVideo === selectedBooking?.id ? 'opacity-50 cursor-not-allowed' : ''}`}>
                      <Upload className="h-5 w-5 mr-2" />
                      {uploadingVideo === selectedBooking?.id ? 'Uploading...' : 'Update Video'}
                      <input
                        type="file"
                        className="hidden"
                        accept="video/*"
                        disabled={uploadingVideo === selectedBooking?.id}
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file && selectedBooking?.id && uploadingVideo !== selectedBooking.id) {
                            handleVideoUpload(selectedBooking.id, file);
                          }
                        }}
                      />
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
