import { CartItem, BusinessInfo, PackageType } from './types';
import { differenceInDays } from 'date-fns';

export function formatPrice(price: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
  }).format(price);
}

export function calculateTotalPrice(cartItems: CartItem[], packageType?: PackageType): number {
  return cartItems.reduce((total, item) => {
    // If no screens selected, item doesn't contribute to total
    if (!item.selectedScreens || item.selectedScreens.length === 0) {
      return total;
    }

    // Calculate based on package type
    if (packageType === 'fixed-price') {
      // Fixed price: ₹12,000 per zone (regardless of screen count)
      return total + 12000;
    }
    else if (packageType === 'per-screen') {
      // ₹500 per screen
      return total + (item.selectedScreens.length * 500);
    }
    else {
      // Fallback to old calculation if no package type
      const startDate = new Date(item.dateRange.startDate!);
      const endDate = new Date(item.dateRange.endDate!);

      // Calculate days difference
      const daysDiff = differenceInDays(endDate, startDate) + 1; // +1 to include both start and end dates

      // Calculate price based on daily rate (yearly price / 365)
      const dailyRate = item.zone.price_year / 365;
      const itemTotal = dailyRate * daysDiff;

      return total + itemTotal;
    }
  }, 0);
}

