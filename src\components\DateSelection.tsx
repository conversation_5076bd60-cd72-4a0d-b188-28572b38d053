import { useState, useEffect } from 'react';
import { DayPicker } from 'react-day-picker';
import { DateRange } from '../types';
import { format, addMonths, addYears, differenceInDays, differenceInMonths, isSameMonth, isSameDay } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import { Calendar, Info } from 'lucide-react';
import 'react-day-picker/dist/style.css';

interface DateSelectionProps {
  dateRange: DateRange;
  onDateChange: (range: DateRange) => void;
  onSubmit: () => void;
}

export function DateSelection({ dateRange, onDateChange, onSubmit }: DateSelectionProps) {
  const navigate = useNavigate();
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());
  const [startDate, setStartDate] = useState<Date | null>(dateRange.startDate);
  const [endDate, setEndDate] = useState<Date | null>(dateRange.endDate);
  const [commitmentYears, setCommitmentYears] = useState<number>(1); // Default to 1 year

  const years = Array.from(
    { length: 5 },
    (_, i) => new Date().getFullYear() + i
  );

  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // Available commitment periods in years
  const commitmentOptions = [1, 2, 3, 4, 5];

  // Update end date when start date or commitment years change
  useEffect(() => {
    if (startDate) {
      // Calculate end date based on selected commitment years
      const calculatedEndDate = addYears(startDate, commitmentYears);
      setEndDate(calculatedEndDate);

      // Update the parent component's state
      onDateChange({
        startDate,
        endDate: calculatedEndDate
      });
    }
  }, [startDate, commitmentYears, onDateChange]);

  const handleRangeSelect = (date: Date | undefined) => {
    if (!date) {
      // Clear selection
      setStartDate(null);
      setEndDate(null);
      onDateChange({
        startDate: null,
        endDate: null,
      });
      return;
    }

    // Update start date - end date will be calculated by useEffect
    setStartDate(date);
  };

  // Handle commitment years change
  const handleCommitmentChange = (years: number) => {
    setCommitmentYears(years);
    // End date will be updated by the useEffect
  };

  const handleMonthYearChange = (month: number, year: number) => {
    setSelectedMonth(month);
    setSelectedYear(year);
  };

  const customStyles = `
    .rdp-nav {
      display: none !important;
    }
    .rdp-day_selected:not(.rdp-day_range_start):not(.rdp-day_range_end) {
      background-color: transparent !important;
      color: inherit !important;
    }
    .rdp-day_range_middle {
      background-color: transparent !important;
      color: inherit !important;
    }
    .rdp-day_range_start,
    .rdp-day_range_end {
      background-color: #23044b !important;
      color: white !important;
      font-weight: bold !important;
      border-radius: 9999px !important;
    }
    .rdp-button:hover:not([disabled]):not(.rdp-day_range_start):not(.rdp-day_range_end) {
      background-color: #EDE9FE !important;
      color: #23044b !important;
    }
    .rdp-nav_button:hover {
      background-color: #EDE9FE !important;
    }
    .rdp-nav_button {
      color: #23044b !important;
    }
    .rdp-day_today {
      font-weight: bold;
      color: #23044b !important;
    }
    .rdp-caption {
      color: #23044b !important;
    }
    .rdp-head_cell {
      color: #23044b !important;
      font-weight: 600 !important;
    }
  `;

  return (
    <div className="space-y-6">
      <style>{customStyles}</style>

      {/* Commitment info banner */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-6">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-purple-600 mr-2 flex-shrink-0" />
          <div>
            <h3 className="text-sm font-medium text-purple-800">Yearly Commitment</h3>
            <p className="mt-1 text-sm text-purple-700">
              All plans require a minimum 12-month commitment. You can select your preferred commitment period below.
            </p>
          </div>
        </div>
      </div>

      {/* Commitment period selector */}
      <div className="mb-6">
        <label htmlFor="commitment-period" className="block text-sm font-medium text-gray-700 mb-2">
          Select Commitment Period
        </label>
        <div className="flex flex-wrap gap-2">
          {commitmentOptions.map(years => (
            <button
              key={years}
              type="button"
              onClick={() => handleCommitmentChange(years)}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                commitmentYears === years
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
              }`}
            >
              {years} {years === 1 ? 'Year' : 'Years'}
            </button>
          ))}
        </div>
      </div>

      <div className="flex space-x-4 mb-4">
        <select
          value={selectedMonth}
          onChange={(e) => handleMonthYearChange(Number(e.target.value), selectedYear)}
          className="block w-1/2 rounded-md border-gray-300 shadow-sm focus:border-[#23044b] focus:ring-[#23044b]"
        >
          {months.map((month, index) => (
            <option key={month} value={index}>
              {month}
            </option>
          ))}
        </select>
        <select
          value={selectedYear}
          onChange={(e) => handleMonthYearChange(selectedMonth, Number(e.target.value))}
          className="block w-1/2 rounded-md border-gray-300 shadow-sm focus:border-[#23044b] focus:ring-[#23044b]"
        >
          {years.map((year) => (
            <option key={year} value={year}>
              {year}
            </option>
          ))}
        </select>
      </div>

      <div className="flex justify-center">
        <div className="border rounded-lg p-4 bg-white shadow-sm">
          <h3 className="text-sm font-medium text-gray-700 mb-2 text-center">Select Start Date</h3>
          <DayPicker
            mode="single"
            selected={startDate || undefined}
            onSelect={handleRangeSelect}
            month={new Date(selectedYear, selectedMonth)}
            showOutsideDays
            fixedWeeks
            disabled={[
              { before: new Date() } // Disable past dates
            ]}
          />
        </div>
      </div>

      {startDate && endDate && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <div className="flex items-center mb-2">
            <Calendar className="h-5 w-5 text-purple-600 mr-2" />
            <h3 className="text-sm font-medium text-gray-900">Your Booking Period</h3>
          </div>
          <div className="flex flex-col sm:flex-row sm:justify-between text-sm text-gray-600">
            <div className="mb-2 sm:mb-0">
              <span className="font-medium">Start:</span> {format(startDate, 'MMMM d, yyyy')}
            </div>
            <div>
              <span className="font-medium">End:</span> {format(endDate, 'MMMM d, yyyy')}
            </div>
          </div>
          <div className="mt-2 pt-2 border-t border-gray-200 text-sm">
            <span className="font-medium text-purple-700">
              {commitmentYears} {commitmentYears === 1 ? 'year' : 'years'} commitment
            </span>
          </div>
        </div>
      )}

      <div className="flex justify-between">
        <button
          onClick={() => navigate('/dashboard')}
          className="rounded-md bg-gray-200 px-4 py-2 text-sm font-semibold text-gray-700 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
        >
          Back to Dashboard
        </button>
        <button
          onClick={onSubmit}
          disabled={!dateRange.startDate || !dateRange.endDate}
          className="rounded-md bg-[#23044b] px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-[#340771] focus:outline-none focus:ring-2 focus:ring-[#23044b] focus:ring-offset-2 disabled:bg-gray-300"
        >
          Next
        </button>
      </div>
    </div>
  );
}
