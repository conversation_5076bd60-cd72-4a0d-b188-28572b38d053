-- Create webhook_logs table for debugging webhook calls
CREATE TABLE IF NOT EXISTS webhook_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webhook_source VARCHAR(50) NOT NULL,
  webhook_event VARCHAR(100),
  webhook_payload JSONB,
  payment_id VARCHAR(100),
  payment_status VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add webhook-related columns to transactions table if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'webhook_event') THEN
    ALTER TABLE transactions ADD COLUMN webhook_event VARCHAR(100);
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'webhook_payload') THEN
    ALTER TABLE transactions ADD COLUMN webhook_payload JSONB;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'webhook_error') THEN
    ALTER TABLE transactions ADD COLUMN webhook_error TEXT;
  END IF;
END
$$;

-- Add payment_status column to bookings table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'bookings' AND column_name = 'payment_status') THEN
    ALTER TABLE bookings ADD COLUMN payment_status VARCHAR(50) DEFAULT 'pending';
  END IF;
END
$$;
