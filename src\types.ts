export interface BusinessInfo {
  businessName: string;
  phone: string;
  email: string;
  listingType: string;
  description: string;
  packageType?: PackageType;
}

export type PackageType = 'fixed-price' | 'per-screen' | '';

export interface Package {
  id: string;
  name: string;
  type: PackageType;
  description: string;
  price: number;
  features: string[];
  minScreens: number;
  isCustomizable: boolean;
}

export interface DateRange {
  startDate: Date | null;
  endDate: Date | null;
}

export interface City {
  id: string;
  name: string;
  description: string;
  image_url: string;
  isavailable: boolean;
  created_at: string;
  updated_at: string;
}

export interface Screen {
  id: string;
  name: string;
  screen_number: string;
  zone_id: string;
  zone_name?: string;
  address: string;
  location: string;
  size: string;
  isavailable: boolean;
  created_at: string;
  updated_at: string;
}

export interface Zone {
  id: string;
  name: string;
  city_id: string;
  city_name?: string;
  sub_zone: string;
  pincode: string | null;
  description: string | null;
  price_year: number;
  image_url: string | null;
  isavailable: boolean;
  created_at: string;
  updated_at: string;
  screens?: Screen[];
}

export interface CartItem {
  zone: Zone;
  dateRange: DateRange;
  videoUrl?: string;
  selectedScreens: Screen[];
}

export interface User {
  id: string;
  email?: string;
  name?: string;
}

export interface Booking {
  id: string;
  zones: Zone[];  // Changed from single zone to array of zones
  start_date: string;
  end_date: string;
  video_url: string | null;
  status: 'Pending' | 'Approved' | 'Fulfilled' | 'Cancelled';
  created_at: string;
  business_name: string;
  phone_number: string;
  email?: string;
  listing_type: string;
  description?: string;
  payment_id?: string;
  payment_status?: 'pending' | 'initiated' | 'authorized' | 'success' | 'failed' | string;
}

interface RazorpayResponse {
  razorpay_payment_id?: string;
  razorpay_order_id?: string;
  error?: any;
}

export interface Coupon {
  id: string;
  code: string;
  type: 'flat' | 'percentage';
  amount: number;
  max_discount?: number;
  min_cart_value: number;
  global_usage_limit?: number;
  per_user_usage_limit?: number;
  start_date: string;
  expiry_date: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CouponUsage {
  id: string;
  coupon_id: string;
  user_id: string;
  usage_count: number;
  last_used_at?: string;
  created_at: string;
}

export interface CouponTier {
  id: string;
  coupon_id: string;
  usage_number: number;
  amount?: number;
  percentage?: number;
  created_at: string;
}

export interface AppliedCoupon {
  coupon: Coupon;
  discount_amount: number;
  usage_number?: number;
}

export interface CouponValidationResult {
  valid: boolean;
  error?: string;
  discount_amount?: number;
  usage_number?: number;
}

interface RazorpayOptions {
  key: string;
  amount: number;
  currency: string;
  name: string;
  description: string;
  image: string;
  handler: (response: RazorpayResponse) => void;
  prefill: {
    name: string;
    email: string;
    contact: string;
  };
  notes: {
    booking_id: string;
    business_name: string;
  };
  theme: {
    color: string;
  };
  modal: {
    ondismiss: () => void;
  };
}
