import { useState, useEffect } from 'react';
import { Zone, CartItem, DateRange, BusinessInfo, PackageType, Screen, City } from '../types';
import { formatPrice } from '../utils';
import { Search, AlertCircle, Monitor, Plus } from 'lucide-react';
import { supabase } from '../lib/supabase';
import { ScreenSelector } from './ScreenSelector';
import { MOCK_ZONES } from '../mockData';
import { Modal } from './Modal';

interface ZoneListProps {
  dateRange: DateRange;
  cartItems: CartItem[];
  onAddToCart: (zone: Zone, selectedScreens: Screen[]) => void;
  onRemoveFromCart: (zoneId: string) => void;
  onNext: () => void;
  businessInfo?: BusinessInfo;
  selectedCities?: City[];
}

export function ZoneList({ dateRange, cartItems, onAddToCart, onRemoveFromCart, onNext, businessInfo, selectedCities = [] }: ZoneListProps) {
  const [zones, setZones] = useState<Zone[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    city: '',
    zone: '',
    subZone: '',
  });
  const [selectedScreensByZone, setSelectedScreensByZone] = useState<Record<string, Screen[]>>({});

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedZone, setSelectedZone] = useState<Zone | null>(null);

  useEffect(() => {
    const fetchZones = async () => {
      try {
        // Fetch zones with screens from database
        const { data: zonesData, error: zonesError } = await supabase
          .from('zones')
          .select(`
            id,
            name,
            city_id,
            city_name,
            sub_zone,
            price_year,
            description,
            image_url,
            isavailable,
            created_at,
            updated_at
          `)
          .eq('isavailable', true)
          .order('created_at', { ascending: false });

        if (zonesError) {
          console.error('Error fetching zones:', zonesError);
          // Fallback to mock data if database fails
          let filteredZones = MOCK_ZONES;
          if (selectedCities.length > 0) {
            const selectedCityIds = selectedCities.map(city => city.id);
            filteredZones = MOCK_ZONES.filter(zone => selectedCityIds.includes(zone.city_id));
          }
          setZones(filteredZones);
          return;
        }

        // Fetch screens for each zone
        const { data: screensData, error: screensError } = await supabase
          .from('screens')
          .select('*')
          .eq('isavailable', true);

        if (screensError) {
          console.error('Error fetching screens:', screensError);
        }

        // Transform zones data and attach screens
        const transformedZones = zonesData?.map(zone => ({
          id: zone.id,
          name: zone.name,
          city_id: zone.city_id,
          city_name: zone.city_name,
          sub_zone: zone.sub_zone,
          price_year: zone.price_year,
          description: zone.description,
          image_url: zone.image_url,
          isavailable: zone.isavailable,
          created_at: zone.created_at,
          updated_at: zone.updated_at,
          screens: screensData?.filter(screen => screen.zone_id === zone.id) || []
        })) || [];

        // Filter zones based on selected cities
        let filteredZones = transformedZones;
        if (selectedCities.length > 0) {
          const selectedCityIds = selectedCities.map(city => city.id);
          filteredZones = transformedZones.filter(zone => selectedCityIds.includes(zone.city_id));
        }

        setZones(filteredZones);
      } catch (err) {
        console.error('Database connection error:', err);
        // Fallback to mock data
        let filteredZones = MOCK_ZONES;
        if (selectedCities.length > 0) {
          const selectedCityIds = selectedCities.map(city => city.id);
          filteredZones = MOCK_ZONES.filter(zone => selectedCityIds.includes(zone.city_id));
        }
        setZones(filteredZones);
      }
    };

    fetchZones();

    // Initialize selected screens from cart items
    const initialSelectedScreens: Record<string, Screen[]> = {};
    cartItems.forEach(item => {
      initialSelectedScreens[item.zone.id] = item.selectedScreens;
    });
    setSelectedScreensByZone(initialSelectedScreens);
  }, [selectedCities, cartItems]);

  const cities = [...new Set(zones.map(zone => zone.city_name).filter(Boolean))];
  const zoneNames = [...new Set(zones.map(zone => zone.name))];
  const subZones = [...new Set(zones.map(zone => zone.sub_zone))];

  // Get the selected package type
  const packageType = businessInfo?.packageType || '';

  // For fixed-price package, we need to enforce zone/sub-zone restrictions
  const isFixedPricePackage = packageType === 'fixed-price';

  // For per-screen package, we allow multiple zones/sub-zones
  const isPerScreenPackage = packageType === 'per-screen';

  // Handle zone selection for fixed-price package (selects all screens in zone)
  const handleZoneSelection = (zone: Zone) => {
    if (isFixedPricePackage) {
      // For fixed-price package, we don't need to select actual screens
      // Just create a representative selection of ~25 screens for display
      const allScreensInZone = zone.screens?.filter(screen => screen.isavailable) || [];

      // For display purposes, limit to ~25 screens (or all if less than 25)
      const displayScreens = allScreensInZone.slice(0, Math.min(25, allScreensInZone.length));

      // Check if this zone is already selected
      const existingItemIndex = cartItems.findIndex(item => item.zone.id === zone.id);

      if (existingItemIndex >= 0) {
        // Zone already selected, remove it
        onRemoveFromCart(zone.id);
      } else {
        // Add this zone to cart with ~25 screens for display
        onAddToCart(zone, displayScreens);
      }
    }
  };

  // Handle screen selection for per-screen package
  const handleScreenSelection = (zone: Zone, screens: Screen[]) => {
    // Update local state
    setSelectedScreensByZone({
      ...selectedScreensByZone,
      [zone.id]: screens
    });

    // Add to cart if screens are selected, otherwise remove from cart
    if (screens.length > 0) {
      onAddToCart(zone, screens);
    } else {
      // If no screens selected, this effectively removes the zone from cart
      onAddToCart(zone, []);
    }
  };

  // Open modal for screen selection (only for per-screen package)
  const openScreenSelectionModal = (zone: Zone) => {
    setSelectedZone(zone);
    setIsModalOpen(true);
  };

  // Close modal
  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedZone(null);
  };

  // Get selected screens for a zone
  const getSelectedScreens = (zoneId: string): Screen[] => {
    return selectedScreensByZone[zoneId] || [];
  };

  // Check if a zone is in the cart
  const isZoneInCart = (zoneId: string): boolean => {
    return cartItems.some(item => item.zone.id === zoneId && item.selectedScreens.length > 0);
  };

  // Calculate total selected screens across all zones
  const totalSelectedScreens = Object.values(selectedScreensByZone)
    .reduce((total, screens) => total + screens.length, 0);

  const filteredZones = zones.filter(zone => {
    const matchesSearch = zone.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (zone.description?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false);
    const matchesCity = !filters.city || zone.city_name === filters.city;
    const matchesZone = !filters.zone || zone.name === filters.zone;
    const matchesSubZone = !filters.subZone || zone.sub_zone === filters.subZone;

    // For fixed-price package, allow multiple zones from the same city
    // No restrictions needed since we're already filtering by selected city
    return matchesSearch && matchesCity && matchesZone && matchesSubZone;
  });

  return (
    <div className="relative min-h-screen pb-8">
      {/* Package information banner */}
      {packageType && (
        <div className={`mb-6 p-4 rounded-lg ${isFixedPricePackage ? 'bg-blue-50 border border-blue-200' : 'bg-green-50 border border-green-200'}`}>
          <div className="flex items-start">
            <AlertCircle className={`h-5 w-5 mr-2 flex-shrink-0 ${isFixedPricePackage ? 'text-blue-500' : 'text-green-500'}`} />
            <div>
              <h3 className="text-sm font-medium mb-1">
                {isFixedPricePackage ? 'Fixed-Price Package Selected' : 'Per-Screen Package Selected'}
              </h3>
              <p className="text-sm text-gray-600">
                {isFixedPricePackage
                  ? 'Select one or more zones. Each zone costs ₹12,000 and includes all screens in that zone (~25 screens per zone).'
                  : 'You can select screens from multiple zones. ₹500 per screen with minimum 25 screens.'}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Make filters stack on mobile and scroll horizontally if needed */}
      <div className="flex flex-col space-y-4 mb-6">
        {/* Search input */}
        <div className="w-full">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search zones..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-full rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
            />
          </div>
        </div>

        {/* Filters container with horizontal scroll on mobile */}
        <div className="flex overflow-x-auto pb-2 gap-2 -mx-4 px-4 sm:mx-0 sm:px-0 sm:overflow-x-visible">
          <select
            value={filters.city}
            onChange={(e) => setFilters({ ...filters, city: e.target.value })}
            className="min-w-[120px] rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
          >
            <option value="">All Cities</option>
            {cities.map(city => (
              <option key={city} value={city}>{city}</option>
            ))}
          </select>

          <select
            value={filters.zone}
            onChange={(e) => setFilters({ ...filters, zone: e.target.value })}
            className="min-w-[120px] rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
          >
            <option value="">All Zones</option>
            {zoneNames.map(zone => (
              <option key={zone} value={zone}>{zone}</option>
            ))}
          </select>

          <select
            value={filters.subZone}
            onChange={(e) => setFilters({ ...filters, subZone: e.target.value })}
            className="min-w-[120px] rounded-md border-gray-300 shadow-sm focus:border-purple-500 focus:ring-purple-500"
          >
            <option value="">All Sub Zones</option>
            {subZones.map(subZone => (
              <option key={subZone} value={subZone}>{subZone}</option>
            ))}
          </select>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredZones.map((zone) => {
          const selectedScreens = getSelectedScreens(zone.id);
          const isInCart = isZoneInCart(zone.id);
          const availableScreensCount = zone.screens?.filter(s => s.isavailable).length || 0;

          return (
            <div
              key={zone.id}
              className="relative flex flex-col overflow-hidden rounded-lg border bg-white hover:shadow-md transition-shadow"
            >
              <div className="aspect-h-3 aspect-w-4">
                <img
                  src={zone.image_url ?? ''}
                  alt={zone.name}
                  className="h-48 w-full object-cover"
                />
              </div>
              <div className="flex flex-1 flex-col p-4">
                <h3 className="text-lg font-semibold text-gray-900">{zone.name}</h3>
                <div className="mt-1 text-sm text-gray-500">
                  <p>{zone.description ?? ''}</p>
                  <p className="mt-2">
                    <span className="font-medium">Location:</span> {`${zone.city_name} > ${zone.sub_zone}`}
                  </p>
                </div>

                <div className="mt-2 flex items-center text-sm text-gray-600">
                  <Monitor className="h-4 w-4 mr-1 text-gray-500" />
                  <span>
                    {isFixedPricePackage
                      ? '~25 screens included'
                      : `${availableScreensCount} screens available`
                    }
                  </span>
                </div>

                {selectedScreens.length > 0 && (
                  <div className="mt-3 p-2 bg-purple-50 rounded-md border border-purple-100">
                    <div className="flex justify-between items-center">
                      <p className="text-sm font-medium text-purple-700">
                        {isFixedPricePackage
                          ? 'Zone selected (~25 screens included)'
                          : `${selectedScreens.length} screens selected`
                        }
                      </p>
                      {!isFixedPricePackage && (
                        <button
                          onClick={() => openScreenSelectionModal(zone)}
                          className="text-xs text-purple-600 hover:text-purple-800 font-medium"
                        >
                          Edit
                        </button>
                      )}
                    </div>
                  </div>
                )}

                <div className="mt-auto pt-4 flex items-center justify-between">
                  <p className="text-lg font-medium text-gray-900">
                    {isFixedPricePackage
                      ? formatPrice(12000)
                      : `${formatPrice(500)} per screen`}
                  </p>

                  {isFixedPricePackage ? (
                    <button
                      onClick={() => handleZoneSelection(zone)}
                      disabled={!zone.isavailable}
                      className={`flex items-center rounded-md px-3 py-1.5 text-sm font-semibold text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:bg-gray-300 ${
                        isInCart ? 'bg-green-600 hover:bg-green-500' : 'bg-purple-600 hover:bg-purple-500'
                      }`}
                    >
                      {isInCart ? 'Remove Zone' : 'Select Zone'}
                      <Plus className="ml-1 h-4 w-4" />
                    </button>
                  ) : (
                    <button
                      onClick={() => openScreenSelectionModal(zone)}
                      disabled={!zone.isavailable}
                      className={`flex items-center rounded-md px-3 py-1.5 text-sm font-semibold text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:bg-gray-300 ${
                        isInCart ? 'bg-green-600 hover:bg-green-500' : 'bg-purple-600 hover:bg-purple-500'
                      }`}
                    >
                      {isInCart ? 'Edit Screens' : 'Select Screens'}
                      <Plus className="ml-1 h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Screen Selection Modal - Only for per-screen package */}
      {selectedZone && !isFixedPricePackage && (
        <Modal
          isOpen={isModalOpen}
          onClose={closeModal}
          title={`Select Screens - ${selectedZone.name}`}
          size="xl"
        >
          <ScreenSelector
            zone={selectedZone}
            packageType={packageType}
            selectedScreens={getSelectedScreens(selectedZone.id)}
            onScreensSelected={(screens) => {
              handleScreenSelection(selectedZone, screens);
              // Don't close modal automatically to allow user to review their selection
            }}
            minScreens={25}
          />

          <div className="mt-6 flex justify-end space-x-3 border-t border-gray-200 pt-4">
            <button
              type="button"
              onClick={closeModal}
              className="rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={closeModal}
              className="rounded-md bg-purple-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-purple-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-purple-600"
            >
              Done
            </button>
          </div>
        </Modal>
      )}

      {cartItems.length > 0 && (
        <div className="mt-6 bg-white rounded-lg border p-4">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-3">
            <div>
              <div className="flex items-center">
                <span className="text-sm font-medium text-gray-700 mr-2">
                  {cartItems.length} zone{cartItems.length !== 1 ? 's' : ''} selected
                </span>
                <span className="text-sm font-medium text-gray-700">
                  {isFixedPricePackage
                    ? `(~${cartItems.length * 25} screens included)`
                    : `(${totalSelectedScreens} screen${totalSelectedScreens !== 1 ? 's' : ''})`
                  }
                </span>
              </div>

              {!isFixedPricePackage && totalSelectedScreens < 25 && (
                <p className="text-xs text-red-600 mt-1">
                  Please select at least 25 screens to continue
                </p>
              )}
            </div>
            <button
              onClick={onNext}
              disabled={cartItems.length === 0 || (!isFixedPricePackage && totalSelectedScreens < 25)}
              className={`w-full sm:w-auto rounded-md px-6 py-2 text-sm font-semibold text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 ${
                (cartItems.length === 0 || (!isFixedPricePackage && totalSelectedScreens < 25))
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-purple-600 hover:bg-purple-500'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}




