// Test script to verify zone deduplication logic
// This simulates the getUniqueZones function to ensure it works correctly

// Mock data that simulates the duplicate zone issue
const mockBookings = [
  {
    id: 'booking-1',
    zones: { id: 'zone-1', name: 'Downtown Mall' },
    booking_zones: null
  },
  {
    id: 'booking-2',
    zones: null,
    booking_zones: [
      { zone_id: 'zone-1', zones: { id: 'zone-1', name: 'Downtown Mall' } },
      { zone_id: 'zone-2', zones: { id: 'zone-2', name: 'Airport Plaza' } }
    ]
  },
  {
    id: 'booking-3',
    zones: { id: 'zone-2', name: 'Airport Plaza' },
    booking_zones: null
  },
  {
    id: 'booking-4',
    zones: null,
    booking_zones: [
      { zone_id: 'zone-1', zones: { id: 'zone-1', name: 'Downtown Mall' } },
      { zone_id: 'zone-3', zones: { id: 'zone-3', name: 'City Center' } }
    ]
  }
];

// Original problematic approach (would create duplicates)
function getZonesOriginalApproach(bookings) {
  console.log('\n🔴 Original Approach (PROBLEMATIC):');
  
  const zones = Array.from(
    new Set([
      ...bookings.map(b => b.zones).filter(Boolean),
      ...bookings.flatMap(b => b.booking_zones?.map(bz => bz.zones) || [])
    ])
  );
  
  console.log('Total zones found:', zones.length);
  console.log('Zone IDs:', zones.map(z => z.id));
  console.log('Zone names:', zones.map(z => z.name));
  
  // Check for duplicates
  const zoneIds = zones.map(z => z.id);
  const uniqueIds = [...new Set(zoneIds)];
  console.log('Unique zone IDs:', uniqueIds.length);
  console.log('Has duplicates:', zoneIds.length !== uniqueIds.length);
  
  return zones;
}

// Fixed approach using Map-based deduplication
function getUniqueZones(bookings) {
  console.log('\n✅ Fixed Approach (WORKING):');
  
  const zoneMap = new Map();
  
  bookings.forEach(booking => {
    // Add zones from direct relationship
    if (booking.zones && booking.zones.id) {
      zoneMap.set(booking.zones.id, booking.zones);
    }
    
    // Add zones from booking_zones relationship
    if (booking.booking_zones && Array.isArray(booking.booking_zones)) {
      booking.booking_zones.forEach(bz => {
        if (bz.zones && bz.zones.id) {
          zoneMap.set(bz.zones.id, bz.zones);
        }
      });
    }
  });
  
  // Convert to array and sort by name for better UX
  const zones = Array.from(zoneMap.values()).sort((a, b) => 
    (a.name || '').localeCompare(b.name || '')
  );
  
  console.log('Total zones found:', zones.length);
  console.log('Zone IDs:', zones.map(z => z.id));
  console.log('Zone names:', zones.map(z => z.name));
  console.log('Zones are sorted:', zones.every((zone, i, arr) => 
    i === 0 || arr[i-1].name <= zone.name
  ));
  
  return zones;
}

// Run the test
console.log('🧪 Testing Zone Deduplication Logic');
console.log('=====================================');

console.log('\n📋 Mock Data:');
console.log('Total bookings:', mockBookings.length);
mockBookings.forEach((booking, i) => {
  console.log(`Booking ${i + 1}:`, {
    id: booking.id,
    directZone: booking.zones?.name || 'None',
    multipleZones: booking.booking_zones?.map(bz => bz.zones.name).join(', ') || 'None'
  });
});

// Test original approach
const originalResult = getZonesOriginalApproach(mockBookings);

// Test fixed approach
const fixedResult = getUniqueZones(mockBookings);

// Comparison
console.log('\n📊 Comparison:');
console.log('Original approach zones:', originalResult.length);
console.log('Fixed approach zones:', fixedResult.length);
console.log('Improvement:', originalResult.length - fixedResult.length, 'duplicates removed');

// Verify React key uniqueness
console.log('\n🔑 React Key Verification:');
const originalKeys = originalResult.map(z => z.id);
const fixedKeys = fixedResult.map(z => z.id);

console.log('Original keys:', originalKeys);
console.log('Fixed keys:', fixedKeys);
console.log('Original has duplicate keys:', originalKeys.length !== new Set(originalKeys).size);
console.log('Fixed has duplicate keys:', fixedKeys.length !== new Set(fixedKeys).size);

// Expected zones
const expectedZones = ['Airport Plaza', 'City Center', 'Downtown Mall'];
const actualZones = fixedResult.map(z => z.name).sort();

console.log('\n✅ Final Verification:');
console.log('Expected zones:', expectedZones);
console.log('Actual zones:', actualZones);
console.log('Test passed:', JSON.stringify(expectedZones) === JSON.stringify(actualZones));

if (JSON.stringify(expectedZones) === JSON.stringify(actualZones)) {
  console.log('\n🎉 SUCCESS: Zone deduplication is working correctly!');
  console.log('✅ No duplicate zones');
  console.log('✅ All zones included');
  console.log('✅ Zones are sorted alphabetically');
  console.log('✅ React keys will be unique');
} else {
  console.log('\n❌ FAILURE: Zone deduplication has issues');
}
