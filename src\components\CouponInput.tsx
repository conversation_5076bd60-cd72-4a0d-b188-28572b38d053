import React, { useState } from 'react';
import { Tag, X, Loader2 } from 'lucide-react';
import { AppliedCoupon } from '../types';
import { applyCoupon, formatDiscountDisplay } from '../lib/coupons';
import { formatPrice } from '../utils';

interface CouponInputProps {
  userId: string;
  cartTotal: number;
  appliedCoupon: AppliedCoupon | null;
  onCouponApplied: (coupon: AppliedCoupon) => void;
  onCouponRemoved: () => void;
}

export function CouponInput({
  userId,
  cartTotal,
  appliedCoupon,
  onCouponApplied,
  onCouponRemoved
}: CouponInputProps) {
  const [couponCode, setCouponCode] = useState('');
  const [isApplying, setIsApplying] = useState(false);
  const [error, setError] = useState('');

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      setError('Please enter a coupon code');
      return;
    }

    setIsApplying(true);
    setError('');

    try {
      const result = await applyCoupon(couponCode.trim(), userId, cartTotal);
      
      if (result) {
        onCouponApplied(result);
        setCouponCode('');
        setError('');
      } else {
        setError('Invalid or expired coupon code');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to apply coupon');
    } finally {
      setIsApplying(false);
    }
  };

  const handleRemoveCoupon = () => {
    onCouponRemoved();
    setCouponCode('');
    setError('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleApplyCoupon();
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border p-4">
      <div className="flex items-center mb-3">
        <Tag className="h-5 w-5 text-purple-600 mr-2" />
        <h3 className="text-lg font-medium text-gray-900">Coupon Code</h3>
      </div>

      {appliedCoupon ? (
        <div className="space-y-3">
          {/* Applied Coupon Display */}
          <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <Tag className="h-4 w-4 text-green-600" />
                </div>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-900">
                  {appliedCoupon.coupon.code}
                </p>
                <p className="text-xs text-green-700">
                  {formatDiscountDisplay(appliedCoupon.coupon)} • 
                  Saved {formatPrice(appliedCoupon.discount_amount)}
                </p>
              </div>
            </div>
            <button
              onClick={handleRemoveCoupon}
              className="flex-shrink-0 text-green-600 hover:text-green-800"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-3">
          {/* Coupon Input */}
          <div className="flex space-x-2">
            <div className="flex-1">
              <input
                type="text"
                value={couponCode}
                onChange={(e) => {
                  setCouponCode(e.target.value.toUpperCase());
                  setError('');
                }}
                onKeyPress={handleKeyPress}
                placeholder="Enter coupon code"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm"
                disabled={isApplying}
              />
            </div>
            <button
              onClick={handleApplyCoupon}
              disabled={isApplying || !couponCode.trim()}
              className="px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isApplying ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-1" />
                  Applying...
                </>
              ) : (
                'Apply'
              )}
            </button>
          </div>

          {/* Error Message */}
          {error && (
            <div className="text-sm text-red-600 bg-red-50 border border-red-200 rounded-md p-2">
              {error}
            </div>
          )}

          {/* Help Text */}
          <p className="text-xs text-gray-500">
            Enter a valid coupon code to get discount on your order
          </p>
        </div>
      )}
    </div>
  );
}
