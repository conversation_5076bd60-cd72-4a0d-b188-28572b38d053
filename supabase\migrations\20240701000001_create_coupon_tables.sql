-- Create coupons table
CREATE TABLE IF NOT EXISTS coupons (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code VARCHAR(50) UNIQUE NOT NULL,
  type VARCHAR(20) CHECK (type IN ('flat', 'percentage')) NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  max_discount DECIMAL(10, 2), -- For percentage coupons, maximum discount amount
  min_cart_value DECIMAL(10, 2) DEFAULT 0,
  global_usage_limit INTEGER, -- NULL means unlimited
  per_user_usage_limit INTEGER, -- NULL means unlimited
  start_date TIMESTAMP NOT NULL,
  expiry_date TIMESTAMP NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create coupon_usages table to track usage per user
CREATE TABLE IF NOT EXISTS coupon_usages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  coupon_id UUID REFERENCES coupons(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  usage_count INTEGER DEFAULT 0,
  last_used_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(coupon_id, user_id)
);

-- Create coupon_tiers table for repeat-use discount adjustments
CREATE TABLE IF NOT EXISTS coupon_tiers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  coupon_id UUID REFERENCES coupons(id) ON DELETE CASCADE,
  usage_number INTEGER NOT NULL, -- Which usage number this tier applies to (1st, 2nd, etc.)
  amount DECIMAL(10, 2), -- Flat amount for this tier
  percentage DECIMAL(5, 2), -- Percentage for this tier
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(coupon_id, usage_number),
  CHECK (
    (amount IS NOT NULL AND percentage IS NULL) OR 
    (amount IS NULL AND percentage IS NOT NULL)
  )
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_coupons_code ON coupons(code);
CREATE INDEX IF NOT EXISTS idx_coupons_active ON coupons(is_active);
CREATE INDEX IF NOT EXISTS idx_coupons_dates ON coupons(start_date, expiry_date);
CREATE INDEX IF NOT EXISTS idx_coupon_usages_coupon_id ON coupon_usages(coupon_id);
CREATE INDEX IF NOT EXISTS idx_coupon_usages_user_id ON coupon_usages(user_id);
CREATE INDEX IF NOT EXISTS idx_coupon_tiers_coupon_id ON coupon_tiers(coupon_id);

-- Add updated_at trigger for coupons table
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_coupons_updated_at BEFORE UPDATE ON coupons
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add coupon_id column to bookings table to track which coupon was used
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'bookings' AND column_name = 'coupon_id') THEN
    ALTER TABLE bookings ADD COLUMN coupon_id UUID REFERENCES coupons(id) ON DELETE SET NULL;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'bookings' AND column_name = 'discount_amount') THEN
    ALTER TABLE bookings ADD COLUMN discount_amount DECIMAL(10, 2) DEFAULT 0;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'bookings' AND column_name = 'original_amount') THEN
    ALTER TABLE bookings ADD COLUMN original_amount DECIMAL(10, 2);
  END IF;
END
$$;
