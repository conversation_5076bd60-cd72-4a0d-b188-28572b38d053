import { supabase } from './supabase';
import { Booking } from '../types';

export async function getUserBookings(userId: string) {
  const { data, error } = await supabase
    .from('bookings')
    .select(`
      id,
      business_name,
      phone_number,
      listing_type,
      description,
      start_date,
      end_date,
      status,
      video_url,
      payment_id,
      payment_status,
      created_at,
      booking_zones!inner(
        zone:zones(
          id,
          name,
          city,
          sub_zone,
          description,
          price_year,
          image_url,
          isavailable
        )
      )
    `)
    .eq('user_id', userId);

  if (error) {
    console.error('Error fetching bookings:', error);
    throw error;
  }

  // Transform the data to match our Booking type
  return data.map((booking: any) => ({
    ...booking,
    zones: booking.booking_zones?.map((bz: any) => bz.zone) || [],
  }));
}

export async function getZoneDetails(zoneId: string) {
  const { data, error } = await supabase
    .from('zones')
    .select(`
      id,
      name,
      city,
      sub_zone,
      pincode,
      description,
      price_year,
      image_url,
      isavailable,
      created_at,
      updated_at
    `)
    .eq('id', zoneId)
    .single();

  if (error) {
    console.error('Error fetching zone details:', error);
    throw error;
  }

  return data;
}

export async function updateBookingVideo(bookingId: string, videoUrl: string): Promise<void> {
  console.log('Updating booking video:', { bookingId, videoUrl });

  const { data, error } = await supabase
    .from('bookings')
    .update({
      video_url: videoUrl,
      updated_at: new Date().toISOString()
    })
    .eq('id', bookingId)
    .select();

  if (error) {
    console.error('Error updating booking video:', error);
    throw error;
  }

  console.log('Booking updated successfully:', data);
}

export async function createBooking(bookingData: {
  user_id: string;
  zones: { id: string }[];
  start_date: string;
  end_date: string;
  business_name: string;
  phone_number: string;
  email?: string;
  listing_type: string;
  description: string;
  video_url: string | null;
}): Promise<string> {
  const { data: booking, error: bookingError } = await supabase
    .from('bookings')
    .insert([{
      user_id: bookingData.user_id,
      start_date: bookingData.start_date,
      end_date: bookingData.end_date,
      business_name: bookingData.business_name,
      phone_number: bookingData.phone_number,
      email: bookingData.email, // Add email field
      listing_type: bookingData.listing_type,
      description: bookingData.description,
      video_url: bookingData.video_url,
      status: 'Pending', // Always Pending initially
      payment_status: 'pending'
    }])
    .select('id')
    .single();

  if (bookingError) {
    console.error('Error creating booking:', bookingError);
    throw bookingError;
  }

  // Create booking-zone relationships
  const bookingZones = bookingData.zones.map(zone => ({
    booking_id: booking.id,
    zone_id: zone.id
  }));

  const { error: zonesError } = await supabase
    .from('booking_zones')
    .insert(bookingZones);

  if (zonesError) {
    console.error('Error creating booking-zone relationships:', zonesError);
    throw zonesError;
  }

  return booking.id;
}

export async function updatePaymentStatus(bookingId: string, paymentId: string, paymentStatus: string = 'initiated'): Promise<void> {
  const { error } = await supabase
    .from('bookings')
    .update({
      payment_id: paymentId,
      payment_status: paymentStatus,
      status: 'Pending'
    })
    .eq('id', bookingId);

  if (error) {
    console.error('Error updating payment status:', error);
    throw error;
  }
}

export async function updateBookingStatus(bookingId: string, status: Booking['status']): Promise<void> {
  const { error } = await supabase
    .from('bookings')
    .update({ status })
    .eq('id', bookingId);

  if (error) {
    console.error('Error updating booking status:', error);
    throw error;
  }
}
