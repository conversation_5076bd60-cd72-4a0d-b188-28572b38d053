import { serve } from "std/http/server.ts";
import { createClient } from "@supabase/supabase-js";
import nodemailer from "nodemailer";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

const emailConfig = {
  host: "smtp.gmail.com",
  port: 465,
  secure: true,
  auth: {
    user: "<EMAIL>",
    pass: "nlnk wltw rgsp zhpp"
  }
};

const ADMIN_EMAIL = "<EMAIL>";

function getCustomerEmailTemplate(booking: any) {
  // Handle both single zone and multiple zones
  let zoneDisplay = '';
  if (booking.zones?.name) {
    // Direct zone relationship
    zoneDisplay = booking.zones.name;
  } else if (booking.booking_zones && booking.booking_zones.length > 0) {
    // Multiple zones through booking_zones
    zoneDisplay = booking.booking_zones.map((bz: any) => bz.zone?.name || 'Unknown Zone').join(', ');
  } else {
    zoneDisplay = 'Unknown Zone';
  }

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4c12ff;">Payment Successful!</h2>
      <p>Dear ${booking.business_name},</p>
      <p>Your payment for booking at 3Shul has been successfully processed.</p>

      <div style="background: #f3f1ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4c12ff;">Booking Details:</h3>
        <ul style="list-style: none; padding: 0;">
          <li><strong>Booking ID:</strong> ${booking.id}</li>
          <li><strong>Business Name:</strong> ${booking.business_name}</li>
          <li><strong>Zone(s):</strong> ${zoneDisplay}</li>
          <li><strong>Contact:</strong> ${booking.phone_number}</li>
          <li><strong>Email:</strong> ${booking.email}</li>
          <li><strong>Duration:</strong> ${new Date(booking.start_date).toLocaleDateString()} to ${new Date(booking.end_date).toLocaleDateString()}</li>
          <li><strong>Amount Paid:</strong> ₹${booking.transactions[0]?.amount || 'N/A'}</li>
        </ul>
      </div>

      <p>Your booking is now <strong>pending approval</strong>. You will receive another email once your booking is approved by our team.</p>
      <p>Thank you for choosing 3Shul. We look forward to hosting you!</p>

      <p>Best regards,<br>3Shul Team</p>
    </div>
  `;
}

function getAdminEmailTemplate(booking: any) {
  // Handle both single zone and multiple zones
  let zoneDisplay = '';
  if (booking.zones?.name) {
    // Direct zone relationship
    zoneDisplay = booking.zones.name;
  } else if (booking.booking_zones && booking.booking_zones.length > 0) {
    // Multiple zones through booking_zones
    zoneDisplay = booking.booking_zones.map((bz: any) => bz.zone?.name || 'Unknown Zone').join(', ');
  } else {
    zoneDisplay = 'Unknown Zone';
  }

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4c12ff;">New Booking Notification</h2>
      <p>A new booking has been confirmed with payment and requires approval:</p>

      <div style="background: #f3f1ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4c12ff;">Booking Details:</h3>
        <ul style="list-style: none; padding: 0;">
          <li><strong>Booking ID:</strong> ${booking.id}</li>
          <li><strong>Business Name:</strong> ${booking.business_name}</li>
          <li><strong>Customer Email:</strong> ${booking.email}</li>
          <li><strong>Customer Phone:</strong> ${booking.phone_number}</li>
          <li><strong>Zone(s):</strong> ${zoneDisplay}</li>
          <li><strong>Duration:</strong> ${new Date(booking.start_date).toLocaleDateString()} to ${new Date(booking.end_date).toLocaleDateString()}</li>
          <li><strong>Amount Paid:</strong> ₹${booking.transactions[0]?.amount || 'N/A'}</li>
          <li><strong>Status:</strong> ${booking.status}</li>
        </ul>
      </div>

      <p>Please review and approve this booking in the admin dashboard.</p>

      <p>Best regards,<br>3Shul System</p>
    </div>
  `;
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { bookingId } = await req.json();

    if (!bookingId) {
      throw new Error('Booking ID is required');
    }

    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Fetch booking details with zones
    const { data: booking, error: bookingError } = await supabaseClient
      .from('bookings')
      .select(`
        *,
        booking_zones (
          zone:zones (
            id,
            name,
            price_year
          )
        ),
        transactions (amount)
      `)
      .eq('id', bookingId)
      .single();

    if (bookingError || !booking) {
      throw new Error('Booking not found');
    }

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: emailConfig.host,
      port: emailConfig.port,
      secure: emailConfig.secure,
      auth: emailConfig.auth,
      tls: {
        rejectUnauthorized: false
      }
    });

    // Verify transporter configuration
    await transporter.verify();
    console.log("Transporter verified successfully");

    // Send email to customer
    const customerInfo = await transporter.sendMail({
      from: `"3Shul Admin" <${emailConfig.auth.user}>`,
      to: booking.email,
      subject: "3Shul Booking Confirmation",
      html: getCustomerEmailTemplate(booking),
      headers: {
        'X-Booking-ID': bookingId
      }
    });
    console.log("Customer email sent successfully:", customerInfo.messageId);

    // Send email to admin
    const adminInfo = await transporter.sendMail({
      from: `"3Shul Admin" <${emailConfig.auth.user}>`,
      to: ADMIN_EMAIL,
      subject: "New Booking Notification - 3Shul",
      html: getAdminEmailTemplate(booking),
      headers: {
        'X-Booking-ID': bookingId
      }
    });
    console.log("Admin email sent successfully:", adminInfo.messageId);

    return new Response(
      JSON.stringify({
        success: true,
        customerMessageId: customerInfo.messageId,
        adminMessageId: adminInfo.messageId
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error("Error:", error.message);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    );
  }
});







