import { BusinessInfo } from '../types';

interface BusinessInfoFormProps {
  businessInfo: BusinessInfo;
  onSubmit: (data: BusinessInfo) => void;
  onChange: (data: Partial<BusinessInfo>) => void;
}

export function BusinessInfoForm({ businessInfo, onChange, onSubmit }: BusinessInfoFormProps) {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(businessInfo);
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, ''); // Remove non-digits
    if (value.length <= 10) { // Only allow up to 10 digits
      onChange({ phone: value });
    }
  };

  const isPhoneValid = businessInfo.phone.length === 10;

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="businessName" className="block text-sm font-medium text-gray-700">
          Business Name
        </label>
        <input
          type="text"
          id="businessName"
          value={businessInfo.businessName}
          onChange={(e) => onChange({ businessName: e.target.value })}
          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-purple-500 focus:outline-none focus:ring-purple-500"
          required
        />
      </div>

      <div>
        <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
          Phone Number
        </label>
        <input
          type="tel"
          id="phone"
          value={businessInfo.phone}
          onChange={handlePhoneChange}
          className={`mt-1 block w-full rounded-md border px-3 py-2 shadow-sm focus:outline-none focus:ring-purple-500 ${
            businessInfo.phone && !isPhoneValid
              ? 'border-red-500 focus:border-red-500'
              : 'border-gray-300 focus:border-purple-500'
          }`}
          required
        />
        {businessInfo.phone && !isPhoneValid && (
          <p className="mt-1 text-sm text-red-600">Please enter a valid 10-digit phone number</p>
        )}
      </div>

      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
          Email Address
        </label>
        <input
          type="email"
          id="email"
          value={businessInfo.email}
          onChange={(e) => onChange({ email: e.target.value })}
          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-purple-500 focus:outline-none focus:ring-purple-500"
          required
        />
      </div>

      <div>
        <label htmlFor="listingType" className="block text-sm font-medium text-gray-700">
          Listing Type
        </label>
        <select
          id="listingType"
          value={businessInfo.listingType}
          onChange={(e) => onChange({ listingType: e.target.value })}
          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-purple-500 focus:outline-none focus:ring-purple-500"
          required
        >
          <option value="">Select a type</option>
          <option value="event">Event</option>
          <option value="exhibition">Exhibition</option>
          <option value="product-display">Product Display</option>
        </select>
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700">
          Brief Description
        </label>
        <textarea
          id="description"
          value={businessInfo.description}
          onChange={(e) => onChange({ description: e.target.value })}
          rows={4}
          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-purple-500 focus:outline-none focus:ring-purple-500"
          required
        />
      </div>

      <div>
        <button
          type="submit"
          disabled={!isPhoneValid && businessInfo.phone.length > 0}
          className={`w-full rounded-md px-4 py-2 text-sm font-semibold text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 ${
            !isPhoneValid && businessInfo.phone.length > 0
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-purple-600 hover:bg-purple-500'
          }`}
        >
          Next
        </button>
      </div>
    </form>
  );
}
