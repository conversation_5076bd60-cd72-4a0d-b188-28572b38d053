import { useState } from 'react';
import { Package, PackageType, BusinessInfo } from '../types';
import { Check } from 'lucide-react';
import { formatPrice } from '../utils';

interface PackageSelectionProps {
  selectedPackage: PackageType;
  businessInfo: BusinessInfo;
  onPackageSelect: (packageType: PackageType) => void;
  onSubmit: () => void;
}

export function PackageSelection({ 
  selectedPackage, 
  businessInfo, 
  onPackageSelect, 
  onSubmit 
}: PackageSelectionProps) {
  const packages: Package[] = [
    {
      id: '1',
      name: 'Fixed-Price Package',
      type: 'fixed-price',
      description: 'Non-customizable pre-defined zones and sub-zones. User chooses one zone and the sub zones inside that zone only.',
      price: 12000,
      features: [
        'Pre-defined zones and sub-zones',
        'User chooses one zone and sub-zones inside that zone only',
        'Minimum 25 screens within that zone/sub-zone',
        'Cost increases proportionally if more than 25 screens are selected',
        '12-month commitment'
      ],
      minScreens: 25,
      isCustomizable: false
    },
    {
      id: '2',
      name: 'Per-Screen Package',
      type: 'per-screen',
      description: 'Fully customizable. User may select multiple zones and multiple sub-zones.',
      price: 500,
      features: [
        'Fully customizable selection',
        'Select multiple zones and multiple sub-zones',
        'Minimum 25 screens total',
        '₹500 per screen',
        'Total cost scales with number of screens selected',
        '12-month commitment'
      ],
      minScreens: 25,
      isCustomizable: true
    }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedPackage) {
      onSubmit();
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">Choose Your Package</h2>
        <p className="mt-2 text-sm text-gray-600">
          Select the package that best fits your advertising needs
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          {packages.map((pkg) => (
            <div 
              key={pkg.id}
              className={`relative rounded-lg border-2 p-6 cursor-pointer transition-all ${
                selectedPackage === pkg.type 
                  ? 'border-purple-600 bg-purple-50' 
                  : 'border-gray-300 hover:border-purple-300'
              }`}
              onClick={() => onPackageSelect(pkg.type)}
            >
              {selectedPackage === pkg.type && (
                <div className="absolute top-4 right-4">
                  <div className="h-6 w-6 rounded-full bg-purple-600 flex items-center justify-center">
                    <Check className="h-4 w-4 text-white" />
                  </div>
                </div>
              )}
              
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{pkg.name}</h3>
                <p className="mt-1 text-sm text-gray-500">{pkg.description}</p>
              </div>
              
              <div className="mb-4">
                <p className="text-2xl font-bold text-gray-900">
                  {pkg.type === 'fixed-price' 
                    ? formatPrice(pkg.price) 
                    : `${formatPrice(pkg.price)} per screen`}
                </p>
                <p className="text-sm text-gray-500">12-month commitment</p>
              </div>
              
              <ul className="space-y-2">
                {pkg.features.map((feature, index) => (
                  <li key={index} className="flex items-start">
                    <Check className="h-5 w-5 text-purple-600 mr-2 flex-shrink-0" />
                    <span className="text-sm text-gray-600">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={!selectedPackage}
            className={`rounded-md px-4 py-2 text-sm font-semibold text-white shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 ${
              !selectedPackage
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-purple-600 hover:bg-purple-500'
            }`}
          >
            Next
          </button>
        </div>
      </form>
    </div>
  );
}
