import { CartItem, BusinessInfo } from '../types';
import { format } from 'date-fns';
import { formatPrice, calculateTotalPrice } from '../utils';
import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';

interface BookingConfirmationProps {
  cartItems: CartItem[];
  onBookAnother: () => void;
  email?: string;
  bookingId?: string;
  businessInfo?: BusinessInfo;
}

export function BookingConfirmation({ cartItems, onBookAnother, email, bookingId, businessInfo }: BookingConfirmationProps) {
  const navigate = useNavigate();

  // Use the same calculation logic as CartSummary
  const packageType = businessInfo?.packageType;
  const totalPrice = calculateTotalPrice(cartItems, packageType);
  const [paymentStatus, setPaymentStatus] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [countdown, setCountdown] = useState(5);

  // Effect for checking payment status
  useEffect(() => {
    let statusIntervalId: number;

    async function checkPaymentStatus() {
      if (!bookingId) {
        console.log('No bookingId provided, skipping payment status check');
        return;
      }

      console.log(`Checking payment status for booking ID: ${bookingId}`);
      try {
        setIsLoading(true);
        const { data, error } = await supabase
          .from('bookings')
          .select('payment_status, status, payment_id')
          .eq('id', bookingId)
          .single();

        if (error) {
          console.error(`Error fetching booking: ${error.message}`);
          throw error;
        }

        console.log(`Booking data:`, data);
        const newStatus = data?.payment_status || null;
        console.log(`Current payment status: ${newStatus}`);
        setPaymentStatus(newStatus);

        // If payment is successful, clear the interval
        if (newStatus === 'success') {
          console.log('Payment status is success, stopping interval checks');
          if (statusIntervalId) clearInterval(statusIntervalId);
        }

        // Reset countdown after each check
        setCountdown(5);
      } catch (error) {
        console.error('Error checking payment status:', error);
      } finally {
        setIsLoading(false);
      }
    }

    // Check immediately on component mount
    checkPaymentStatus();

    // Then check every 5 seconds until payment is successful
    statusIntervalId = window.setInterval(checkPaymentStatus, 5000);

    // Clean up interval on component unmount
    return () => {
      if (statusIntervalId) clearInterval(statusIntervalId);
    };
  }, [bookingId]);

  // Effect for countdown timer
  useEffect(() => {
    // Only run countdown if payment is not yet successful
    if (paymentStatus !== 'success' && !isLoading) {
      const countdownIntervalId = window.setInterval(() => {
        setCountdown((prevCount) => {
          if (prevCount <= 1) {
            return 5; // Reset to 5 when it reaches 0
          }
          return prevCount - 1;
        });
      }, 1000);

      return () => clearInterval(countdownIntervalId);
    }
  }, [paymentStatus, isLoading]);

  // Show loading state while checking payment status
  if (isLoading) {
    return (
      <div className="text-center space-y-6">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-purple-600 border-t-transparent mx-auto" />
        <p className="text-lg font-medium text-gray-900">Checking payment status...</p>
      </div>
    );
  }

  // Only show confirmation when payment status is 'success'
  // For all other statuses, show a waiting message
  if (paymentStatus !== 'success') {
    return (
      <div className="text-center space-y-6">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
          <svg
            className="h-6 w-6 text-yellow-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-900">Waiting for payment confirmation</h2>
        <p className="text-gray-600">
          Your payment is being processed. Please wait while we confirm your payment.
          This may take a few moments.
        </p>
        <p className="text-sm text-gray-500 mt-2">
          Current status: <span className="font-medium">{paymentStatus || 'pending'}</span>
        </p>
        <p className="text-sm text-gray-500 mt-4">
          The page will automatically update when your payment is confirmed.
          Checking again in <span className="font-medium">{countdown}</span> seconds...
        </p>
        <div className="flex space-x-4 justify-center">
          <button
            onClick={() => window.location.reload()}
            className="mt-4 rounded-md bg-gray-200 px-4 py-2 text-sm font-semibold text-gray-700 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            Refresh Page
          </button>
          <button
            onClick={async () => {
              try {
                setIsLoading(true);
                console.log('Manual update requested for booking ID:', bookingId);
                const response = await fetch(`https://xirnspqtaoyzsqlbbhcl.supabase.co/functions/v1/razorpay-webhook?booking_id=${bookingId}`, {
                  method: 'GET',
                  headers: {
                    'Authorization': `Bearer ${process.env.REACT_APP_SUPABASE_ANON_KEY || ''}`
                  }
                });
                const data = await response.json();
                console.log('Manual update response:', data);
                if (data.success) {
                  setPaymentStatus('success');
                }
              } catch (error) {
                console.error('Error manually updating payment status:', error);
              } finally {
                setIsLoading(false);
              }
            }}
            className="mt-4 rounded-md bg-purple-600 px-4 py-2 text-sm font-semibold text-white hover:bg-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
          >
            Force Update Status
          </button>
        </div>
      </div>
    );
  }

  // If payment is initiated or successful, show confirmation
  return (
    <div className="text-center space-y-6">
      <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
        <svg
          className="h-6 w-6 text-green-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M5 13l4 4L19 7"
          />
        </svg>
      </div>
      <h2 className="text-2xl font-bold text-gray-900">Thank you for your booking!</h2>
      <p className="text-gray-600">
        Your booking has been confirmed. You will receive a confirmation email shortly.
      </p>

      {/* Booking Details */}
      <div className="mt-8 space-y-4 text-left">
        {bookingId && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-500">Booking ID</p>
            <p className="font-medium">{bookingId}</p>
          </div>
        )}

        {/* Package Type and Zone Count Summary */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm font-medium text-blue-900">
                Package: {packageType === 'fixed-price' ? 'Fixed-Price Package' : packageType === 'per-screen' ? 'Per-Screen Package' : 'Standard Package'}
              </p>
              <p className="text-sm text-blue-700">
                {cartItems.length} zone{cartItems.length !== 1 ? 's' : ''} selected
                {packageType === 'per-screen' && (
                  <span> • {cartItems.reduce((total, item) => total + item.selectedScreens.length, 0)} screens total</span>
                )}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border divide-y">
          {cartItems.map((item) => {
            const isFixedPricePackage = packageType === 'fixed-price';
            const isPerScreenPackage = packageType === 'per-screen';

            let itemPrice = 0;
            if (isFixedPricePackage) {
              itemPrice = 12000; // Fixed price per zone
            } else if (isPerScreenPackage) {
              itemPrice = item.selectedScreens.length * 500; // ₹500 per screen
            } else {
              itemPrice = item.zone.price_year; // Fallback to zone price
            }

            return (
              <div key={item.zone.id} className="p-4">
                <h3 className="font-semibold text-gray-900">{item.zone.name}</h3>
                <p className="mt-1 text-sm text-gray-500">{item.zone.description}</p>
                <div className="mt-2 text-sm text-gray-700">
                  {format(item.dateRange.startDate!, 'MMM d, yyyy')} -{' '}
                  {format(item.dateRange.endDate!, 'MMM d, yyyy')}
                </div>

                {/* Package-specific pricing display */}
                <div className="mt-2 text-sm font-medium text-gray-900">
                  {isFixedPricePackage ? (
                    <>
                      Zone price: <span className="font-bold">{formatPrice(12000)}</span>
                      <span className="text-gray-500 ml-2">(includes ~25 screens)</span>
                    </>
                  ) : isPerScreenPackage ? (
                    <>
                      {item.selectedScreens.length} screens × {formatPrice(500)} =
                      <span className="font-bold ml-1">{formatPrice(itemPrice)}</span>
                    </>
                  ) : (
                    <>
                      {formatPrice(item.zone.price_year)} / year
                    </>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex justify-between">
            <p className="font-medium">Total Amount</p>
            <p className="font-medium">{formatPrice(totalPrice)}</p>
          </div>
        </div>

        {email && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-500">Confirmation email will be sent to</p>
            <p className="font-medium">{email}</p>
          </div>
        )}
      </div>

      <div className="mt-8 flex justify-center space-x-4">
        <button
          onClick={() => navigate('/dashboard')}
          className="rounded-md bg-gray-200 px-4 py-2 text-sm font-semibold text-gray-700 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
        >
          Back to Dashboard
        </button>
        <button
          onClick={onBookAnother}
          className="rounded-md bg-purple-600 px-4 py-2 text-sm font-semibold text-white shadow-sm hover:bg-purple-500 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
        >
          Book Another Zone
        </button>
      </div>
    </div>
  );
}



