import { Zone, Screen, City } from './types';

// Mock screens data
export const MOCK_SCREENS: Screen[] = [
  // Zone 1 screens (Mumbai - Colaba)
  { id: '101', name: 'Screen A1', screen_number: 'SCR-001', zone_id: '1', zone_name: 'Premium Display Zone A', address: 'Colaba Causeway, Near Taj Hotel', location: 'Main Entrance', size: '55"', isavailable: true, created_at: '', updated_at: '' },
  { id: '102', name: 'Screen A2', screen_number: 'SCR-002', zone_id: '1', zone_name: 'Premium Display Zone A', address: 'Colaba Market, Food Court Area', location: 'Food Court', size: '42"', isavailable: true, created_at: '', updated_at: '' },
  { id: '103', name: 'Screen A3', screen_number: 'SCR-003', zone_id: '1', zone_name: 'Premium Display Zone A', address: 'Regal Cinema Building, Lobby', location: 'Elevator Lobby', size: '32"', isavailable: true, created_at: '', updated_at: '' },
  { id: '104', name: 'Screen A4', screen_number: 'SCR-004', zone_id: '1', zone_name: 'Premium Display Zone A', address: 'Gateway of India, Waiting Area', location: 'Waiting Area', size: '55"', isavailable: true, created_at: '', updated_at: '' },
  { id: '105', name: 'Screen A5', screen_number: 'SCR-005', zone_id: '1', zone_name: 'Premium Display Zone A', address: 'Colaba Police Station, Corridor', location: 'Corridor 1', size: '42"', isavailable: true, created_at: '', updated_at: '' },
  { id: '106', name: 'Screen A6', screen_number: 'SCR-006', zone_id: '1', zone_name: 'Premium Display Zone A', address: 'Navy Nagar, Main Corridor', location: 'Corridor 2', size: '42"', isavailable: true, created_at: '', updated_at: '' },
  { id: '107', name: 'Screen A7', screen_number: 'SCR-007', zone_id: '1', zone_name: 'Premium Display Zone A', address: 'Strand Cinema, Corridor 3', location: 'Corridor 3', size: '42"', isavailable: true, created_at: '', updated_at: '' },
  { id: '108', name: 'Screen A8', screen_number: 'SCR-008', zone_id: '1', zone_name: 'Premium Display Zone A', address: 'Colaba Market, Restroom Area', location: 'Restroom Area', size: '32"', isavailable: true, created_at: '', updated_at: '' },
  { id: '109', name: 'Screen A9', screen_number: 'SCR-009', zone_id: '1', zone_name: 'Premium Display Zone A', address: 'Taj Hotel, Exit Gate', location: 'Exit Gate', size: '55"', isavailable: true, created_at: '', updated_at: '' },
  { id: '110', name: 'Screen A10', screen_number: 'SCR-010', zone_id: '1', zone_name: 'Premium Display Zone A', address: 'Gateway Plaza, Parking Entrance', location: 'Parking Entrance', size: '42"', isavailable: true, created_at: '', updated_at: '' },
  { id: '111', name: 'Screen A11', zone_id: '1', location: 'Escalator 1', size: '32"', isavailable: true },
  { id: '112', name: 'Screen A12', zone_id: '1', location: 'Escalator 2', size: '32"', isavailable: true },
  { id: '113', name: 'Screen A13', zone_id: '1', location: 'Information Desk', size: '55"', isavailable: true },
  { id: '114', name: 'Screen A14', zone_id: '1', location: 'Security Checkpoint', size: '42"', isavailable: true },
  { id: '115', name: 'Screen A15', zone_id: '1', location: 'Retail Area 1', size: '55"', isavailable: true },
  { id: '116', name: 'Screen A16', zone_id: '1', location: 'Retail Area 2', size: '55"', isavailable: true },
  { id: '117', name: 'Screen A17', zone_id: '1', location: 'Retail Area 3', size: '55"', isavailable: true },
  { id: '118', name: 'Screen A18', zone_id: '1', location: 'Retail Area 4', size: '55"', isavailable: true },
  { id: '119', name: 'Screen A19', zone_id: '1', location: 'Retail Area 5', size: '55"', isavailable: true },
  { id: '120', name: 'Screen A20', zone_id: '1', location: 'Retail Area 6', size: '55"', isavailable: true },
  { id: '121', name: 'Screen A21', zone_id: '1', location: 'Retail Area 7', size: '55"', isavailable: true },
  { id: '122', name: 'Screen A22', zone_id: '1', location: 'Retail Area 8', size: '55"', isavailable: true },
  { id: '123', name: 'Screen A23', zone_id: '1', location: 'Retail Area 9', size: '55"', isavailable: true },
  { id: '124', name: 'Screen A24', zone_id: '1', location: 'Retail Area 10', size: '55"', isavailable: true },
  { id: '125', name: 'Screen A25', zone_id: '1', location: 'Retail Area 11', size: '55"', isavailable: true },
  { id: '126', name: 'Screen A26', zone_id: '1', location: 'Retail Area 12', size: '55"', isavailable: false },
  { id: '127', name: 'Screen A27', zone_id: '1', location: 'Retail Area 13', size: '55"', isavailable: true },
  { id: '128', name: 'Screen A28', zone_id: '1', location: 'Retail Area 14', size: '55"', isavailable: true },
  { id: '129', name: 'Screen A29', zone_id: '1', location: 'Retail Area 15', size: '55"', isavailable: true },
  { id: '130', name: 'Screen A30', zone_id: '1', location: 'Retail Area 16', size: '55"', isavailable: true },

  // Zone 2 screens
  { id: '201', name: 'Screen B1', zone_id: '2', location: 'Main Entrance', size: '55"', isavailable: true },
  { id: '202', name: 'Screen B2', zone_id: '2', location: 'Food Court', size: '42"', isavailable: true },
  { id: '203', name: 'Screen B3', zone_id: '2', location: 'Elevator Lobby', size: '32"', isavailable: true },
  { id: '204', name: 'Screen B4', zone_id: '2', location: 'Waiting Area', size: '55"', isavailable: true },
  { id: '205', name: 'Screen B5', zone_id: '2', location: 'Corridor 1', size: '42"', isavailable: true },
  { id: '206', name: 'Screen B6', zone_id: '2', location: 'Corridor 2', size: '42"', isavailable: true },
  { id: '207', name: 'Screen B7', zone_id: '2', location: 'Corridor 3', size: '42"', isavailable: true },
  { id: '208', name: 'Screen B8', zone_id: '2', location: 'Restroom Area', size: '32"', isavailable: true },
  { id: '209', name: 'Screen B9', zone_id: '2', location: 'Exit Gate', size: '55"', isavailable: true },
  { id: '210', name: 'Screen B10', zone_id: '2', location: 'Parking Entrance', size: '42"', isavailable: true },
  { id: '211', name: 'Screen B11', zone_id: '2', location: 'Escalator 1', size: '32"', isavailable: true },
  { id: '212', name: 'Screen B12', zone_id: '2', location: 'Escalator 2', size: '32"', isavailable: true },
  { id: '213', name: 'Screen B13', zone_id: '2', location: 'Information Desk', size: '55"', isavailable: true },
  { id: '214', name: 'Screen B14', zone_id: '2', location: 'Security Checkpoint', size: '42"', isavailable: true },
  { id: '215', name: 'Screen B15', zone_id: '2', location: 'Retail Area 1', size: '55"', isavailable: true },
  { id: '216', name: 'Screen B16', zone_id: '2', location: 'Retail Area 2', size: '55"', isavailable: true },
  { id: '217', name: 'Screen B17', zone_id: '2', location: 'Retail Area 3', size: '55"', isavailable: true },
  { id: '218', name: 'Screen B18', zone_id: '2', location: 'Retail Area 4', size: '55"', isavailable: true },
  { id: '219', name: 'Screen B19', zone_id: '2', location: 'Retail Area 5', size: '55"', isavailable: true },
  { id: '220', name: 'Screen B20', zone_id: '2', location: 'Retail Area 6', size: '55"', isavailable: true },
  { id: '221', name: 'Screen B21', zone_id: '2', location: 'Retail Area 7', size: '55"', isavailable: true },
  { id: '222', name: 'Screen B22', zone_id: '2', location: 'Retail Area 8', size: '55"', isavailable: true },
  { id: '223', name: 'Screen B23', zone_id: '2', location: 'Retail Area 9', size: '55"', isavailable: true },
  { id: '224', name: 'Screen B24', zone_id: '2', location: 'Retail Area 10', size: '55"', isavailable: true },
  { id: '225', name: 'Screen B25', zone_id: '2', location: 'Retail Area 11', size: '55"', isavailable: true },
  { id: '226', name: 'Screen B26', zone_id: '2', location: 'Retail Area 12', size: '55"', isavailable: false },
  { id: '227', name: 'Screen B27', zone_id: '2', location: 'Retail Area 13', size: '55"', isavailable: true },
  { id: '228', name: 'Screen B28', zone_id: '2', location: 'Retail Area 14', size: '55"', isavailable: true },
  { id: '229', name: 'Screen B29', zone_id: '2', location: 'Retail Area 15', size: '55"', isavailable: true },
  { id: '230', name: 'Screen B30', zone_id: '2', location: 'Retail Area 16', size: '55"', isavailable: true },

  // Zone 3 screens (Delhi - Connaught Place) - Adding more screens for pagination testing
  { id: '301', name: 'Screen C1', screen_number: 'SCR-301', zone_id: '3', zone_name: 'Shopping Mall Zone C', address: 'Connaught Place, Central Park', location: 'Main Entrance', size: '55"', isavailable: true, created_at: '', updated_at: '' },
  { id: '302', name: 'Screen C2', screen_number: 'SCR-302', zone_id: '3', zone_name: 'Shopping Mall Zone C', address: 'CP Metro Station, Food Court', location: 'Food Court', size: '42"', isavailable: true, created_at: '', updated_at: '' },
  { id: '303', name: 'Screen C3', screen_number: 'SCR-303', zone_id: '3', zone_name: 'Shopping Mall Zone C', address: 'Palika Bazaar, Elevator Lobby', location: 'Elevator Lobby', size: '32"', isavailable: true, created_at: '', updated_at: '' },
  { id: '304', name: 'Screen C4', screen_number: 'SCR-304', zone_id: '3', zone_name: 'Shopping Mall Zone C', address: 'Janpath Market, Waiting Area', location: 'Waiting Area', size: '55"', isavailable: true, created_at: '', updated_at: '' },
  { id: '305', name: 'Screen C5', screen_number: 'SCR-305', zone_id: '3', zone_name: 'Shopping Mall Zone C', address: 'Regal Building, Corridor 1', location: 'Corridor 1', size: '42"', isavailable: true, created_at: '', updated_at: '' },

  // Adding more screens for pagination testing (Zone 1 - Mumbai)
  ...Array.from({ length: 50 }, (_, i) => ({
    id: `1${(i + 31).toString().padStart(2, '0')}`,
    name: `Screen A${i + 31}`,
    screen_number: `SCR-${(i + 31).toString().padStart(3, '0')}`,
    zone_id: '1',
    zone_name: 'Premium Display Zone A',
    address: `Colaba Area ${i + 1}, Mumbai`,
    location: `Location ${i + 1}`,
    size: ['32"', '42"', '55"'][i % 3],
    isavailable: true,
    created_at: '',
    updated_at: ''
  })),

  // Adding more screens for Zone 2 - Mumbai
  ...Array.from({ length: 30 }, (_, i) => ({
    id: `2${(i + 31).toString().padStart(2, '0')}`,
    name: `Screen B${i + 31}`,
    screen_number: `SCR-${(i + 231).toString().padStart(3, '0')}`,
    zone_id: '2',
    zone_name: 'Business District Zone B',
    address: `BKC Area ${i + 1}, Mumbai`,
    location: `Business Location ${i + 1}`,
    size: ['32"', '42"', '55"'][i % 3],
    isavailable: true,
    created_at: '',
    updated_at: ''
  })),

  // Adding more screens for Zone 3 - Delhi
  ...Array.from({ length: 40 }, (_, i) => ({
    id: `3${(i + 6).toString().padStart(2, '0')}`,
    name: `Screen C${i + 6}`,
    screen_number: `SCR-${(i + 306).toString().padStart(3, '0')}`,
    zone_id: '3',
    zone_name: 'Shopping Mall Zone C',
    address: `CP Area ${i + 1}, Delhi`,
    location: `Mall Location ${i + 1}`,
    size: ['32"', '42"', '55"'][i % 3],
    isavailable: true,
    created_at: '',
    updated_at: ''
  }))
];

// Mock zones data with screens
export const MOCK_ZONES: Zone[] = [
  {
    id: '1',
    name: 'Premium Display Zone A',
    city_id: 'city-1',
    city_name: 'Mumbai',
    sub_zone: 'Colaba',
    pincode: null,
    description: 'High-traffic area perfect for product displays and exhibitions',
    price_year: 1500000,
    image_url: 'https://images.unsplash.com/photo-1497366216548-37526070297c',
    isavailable: true,
    created_at: '',
    updated_at: '',
    screens: MOCK_SCREENS.filter(screen => screen.zone_id === '1')
  },
  {
    id: '2',
    name: 'Business District Zone B',
    city_id: 'city-1',
    city_name: 'Mumbai',
    sub_zone: 'Bandra Kurla Complex',
    pincode: null,
    description: 'Corporate hub with high-end audience and business professionals',
    price_year: 1800000,
    image_url: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab',
    isavailable: true,
    created_at: '',
    updated_at: '',
    screens: MOCK_SCREENS.filter(screen => screen.zone_id === '2')
  },
  {
    id: '3',
    name: 'Shopping Mall Zone C',
    city_id: 'city-2',
    city_name: 'Delhi',
    sub_zone: 'Connaught Place',
    pincode: null,
    description: 'Busy shopping area with diverse customer base',
    price_year: 1200000,
    image_url: 'https://images.unsplash.com/photo-1519567241046-7f570eee3ce6',
    isavailable: true,
    created_at: '',
    updated_at: '',
    screens: MOCK_SCREENS.filter(screen => screen.zone_id === '3')
  }
];

// Mock cities data
export const MOCK_CITIES: City[] = [
  {
    id: 'city-1',
    name: 'Mumbai',
    description: 'Financial capital of India with high-traffic commercial areas and premium shopping districts.',
    image_url: 'https://images.unsplash.com/photo-1570168007204-dfb528c6958f',
    isavailable: true,
    created_at: '',
    updated_at: ''
  },
  {
    id: 'city-2',
    name: 'Delhi',
    description: 'National capital with diverse commercial zones, government offices, and bustling markets.',
    image_url: 'https://images.unsplash.com/photo-1587474260584-136574528ed5',
    isavailable: true,
    created_at: '',
    updated_at: ''
  },
  {
    id: 'city-3',
    name: 'Bangalore',
    description: 'IT hub of India with modern office complexes, tech parks, and vibrant commercial areas.',
    image_url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
    isavailable: true,
    created_at: '',
    updated_at: ''
  },
  {
    id: 'city-4',
    name: 'Chennai',
    description: 'Major port city with industrial zones, automotive hubs, and traditional commercial areas.',
    image_url: 'https://images.unsplash.com/photo-1582510003544-4d00b7f74220',
    isavailable: true,
    created_at: '',
    updated_at: ''
  },
  {
    id: 'city-5',
    name: 'Hyderabad',
    description: 'Growing tech city with HITEC City, pharmaceutical companies, and modern commercial districts.',
    image_url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96',
    isavailable: true,
    created_at: '',
    updated_at: ''
  },
  {
    id: 'city-6',
    name: 'Pune',
    description: 'Educational and IT hub with numerous colleges, tech companies, and commercial centers.',
    image_url: 'https://images.unsplash.com/photo-1595658658481-d53d3f999875',
    isavailable: true,
    created_at: '',
    updated_at: ''
  }
];
