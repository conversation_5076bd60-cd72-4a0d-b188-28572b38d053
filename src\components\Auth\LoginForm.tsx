import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

type AuthMode = 'signin' | 'signup' | 'forgot-password';

export function LoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [mode, setMode] = useState<AuthMode>('signin');
  const [resetSent, setResetSent] = useState(false);
  const { login, signUp } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      if (mode === 'signup') {
        if (password !== confirmPassword) {
          throw new Error('Passwords do not match');
        }
        await signUp(email, password);
        navigate('/dashboard');
      } else if (mode === 'forgot-password') {
        setResetSent(true);
      } else {
        // Login mode
        try {
          await login(email, password);
          // Only navigate if login was successful
          navigate('/dashboard');
        } catch (error: any) {
          setError(error.message);
          // Don't navigate on error
          return;
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#23044b] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-xl">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {mode === 'signin' && 'Sign in to your account'}
            {mode === 'signup' && 'Create your account'}
            {mode === 'forgot-password' && 'Reset your password'}
          </h2>
        </div>

        {mode === 'forgot-password' && resetSent ? (
          <div className="text-center">
            <div className="rounded-md bg-green-50 p-4">
              <p className="text-sm text-green-700">
                If an account exists with {email}, you will receive a password reset link shortly.
              </p>
            </div>
            <button
              onClick={() => {
                setMode('signin');
                setResetSent(false);
              }}
              className="mt-4 text-sm font-medium text-[#23044b] hover:text-[#340771]"
            >
              Return to sign in
            </button>
          </div>
        ) : (
          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            {error && (
              <div className="rounded-md bg-red-50 p-4">
                <div className="text-sm text-red-700">{error}</div>
              </div>
            )}

            <div className="rounded-md shadow-sm -space-y-px">
              <div>
                <label htmlFor="email-address" className="sr-only">
                  Email address
                </label>
                <input
                  id="email-address"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="appearance-none rounded-t-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#23044b] focus:border-[#23044b] focus:z-10 sm:text-sm"
                  placeholder="Email address"
                />
              </div>
              {mode !== 'forgot-password' && (
                <div>
                  <label htmlFor="password" className="sr-only">
                    Password
                  </label>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete={mode === 'signup' ? 'new-password' : 'current-password'}
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className={`appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 ${
                      mode === 'signup' ? '' : 'rounded-b-md'
                    } focus:outline-none focus:ring-[#23044b] focus:border-[#23044b] focus:z-10 sm:text-sm`}
                    placeholder="Password"
                  />
                </div>
              )}
              {mode === 'signup' && (
                <div>
                  <label htmlFor="confirm-password" className="sr-only">
                    Confirm Password
                  </label>
                  <input
                    id="confirm-password"
                    name="confirm-password"
                    type="password"
                    autoComplete="new-password"
                    required
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="appearance-none rounded-b-md relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-[#23044b] focus:border-[#23044b] focus:z-10 sm:text-sm"
                    placeholder="Confirm password"
                  />
                </div>
              )}
            </div>

            {mode === 'signin' && (
              <div className="flex items-center justify-between">
                <div className="text-sm">
                  <button
                    type="button"
                    onClick={() => setMode('forgot-password')}
                    className="font-medium text-[#23044b] hover:text-[#340771]"
                  >
                    Forgot your password?
                  </button>
                </div>
                <div className="text-sm">
                  <button
                    type="button"
                    onClick={() => setMode('signup')}
                    className="font-medium text-[#23044b] hover:text-[#340771]"
                  >
                    Create an account
                  </button>
                </div>
              </div>
            )}

            {mode !== 'signin' && (
              <div className="text-sm text-center">
                <button
                  type="button"
                  onClick={() => setMode('signin')}
                  className="font-medium text-[#23044b] hover:text-[#340771]"
                >
                  Back to sign in
                </button>
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-[#23044b] hover:bg-[#340771] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#23044b]"
              >
                {loading ? (
                  <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                    <svg
                      className="animate-spin h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  </span>
                ) : null}
                {mode === 'signin' && 'Sign in'}
                {mode === 'signup' && 'Create account'}
                {mode === 'forgot-password' && 'Reset password'}
              </button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}

